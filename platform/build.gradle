/*
 * Copyright contributors to Hyperledger Besu.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
plugins {
  id 'java-platform'
  id 'com.diffplug.spotless'
}

repositories {
  mavenCentral()
}

javaPlatform {
  allowDependencies()
}

dependencies {
  api platform('com.fasterxml.jackson:jackson-bom:2.18.2')
  api platform('io.grpc:grpc-bom:1.70.0')
  api platform('io.netty:netty-bom:4.1.118.Final')
  api platform('io.opentelemetry:opentelemetry-bom:1.47.0')
  api platform('io.prometheus:prometheus-metrics-bom:1.3.5')
  api platform('io.vertx:vertx-stack-depchain:4.5.13')
  api platform('org.apache.logging.log4j:log4j-bom:2.24.3')
  api platform('org.assertj:assertj-bom:3.27.3')
  api platform('org.immutables:bom:2.10.1')
  api platform('org.junit:junit-bom:5.11.4')
  api platform('org.mockito:mockito-bom:5.15.2')
  api platform('org.slf4j:slf4j-bom:2.0.16')

  constraints {
    api project(':acceptance-tests:dsl')
    api project(':app')
    api project(':config')
    api project(':consensus:clique')
    api project(':consensus:common')
    api project(':consensus:ibft')
    api project(':consensus:ibftlegacy')
    api project(':consensus:merge')
    api project(':consensus:qbft')
    api project(':consensus:qbft-core')
    api project(':crypto:algorithms')
    api project(':crypto:services')
    api project(':datatypes')
    api project(':ethereum:api')
    api project(':ethereum:blockcreation')
    api project(':ethereum:core')
    api project(':ethereum:eth')
    api project(':ethereum:p2p')
    api project(':ethereum:permissioning')
    api project(':ethereum:referencetests')
    api project(':ethereum:rlp')
    api project(':ethereum:trie')
    api project(':evm')
    api project(':metrics:core')
    api project(':plugin-api')
    api project(':services:kvstore')
    api project(':services:pipeline')
    api project(':services:tasks')
    api project(':testutil')
    api project(':util')

    api 'com.github.ben-manes.caffeine:caffeine:3.2.0'

    api 'com.github.oshi:oshi-core:6.6.6'

    api 'com.google.auto.service:auto-service:1.1.1'

    api 'com.google.dagger:dagger-compiler:2.55'
    api 'com.google.dagger:dagger:2.55'

    api 'com.google.guava:guava:33.4.0-jre'

    api 'com.google.protobuf:protobuf-java:3.25.5'

    api 'com.graphql-java:graphql-java:22.3'

    api 'com.splunk.logging:splunk-library-javalogging:1.11.8'

    api 'com.squareup.okhttp3:okhttp:4.12.0'

    api 'commons-io:commons-io:2.18.0'

    api 'commons-net:commons-net:3.11.1'

    api 'dnsjava:dnsjava:3.6.3'

    api 'info.picocli:picocli:4.7.6'
    api 'info.picocli:picocli-codegen:4.7.6'

    api 'io.opentelemetry.instrumentation:opentelemetry-okhttp-3.0:2.13.1-alpha'
    api 'io.opentelemetry.proto:opentelemetry-proto:1.5.0-alpha'
    api 'io.opentelemetry.semconv:opentelemetry-semconv:1.30.0'
    api 'io.opentelemetry.semconv:opentelemetry-semconv-incubating:1.30.0-alpha'

    api 'io.opentracing:opentracing-api:0.33.0'
    api 'io.opentracing:opentracing-util:0.33.0'
    api 'io.opentracing.contrib:opentracing-okhttp3:3.0.0'

    api 'io.pkts:pkts-core:3.0.10'

    api 'io.consensys.tuweni:tuweni-bytes:2.7.1'
    api 'io.consensys.tuweni:tuweni-config:2.7.1'
    api 'io.consensys.tuweni:tuweni-concurrent:2.7.1'
    api 'io.consensys.tuweni:tuweni-crypto:2.7.1'
    api 'io.consensys.tuweni:tuweni-io:2.7.1'
    api 'io.consensys.tuweni:tuweni-net:2.7.1'
    api 'io.consensys.tuweni:tuweni-rlp:2.7.1'
    api 'io.consensys.tuweni:tuweni-toml:2.7.1'
    api 'io.consensys.tuweni:tuweni-units:2.7.1'

    api 'jakarta.validation:jakarta.validation-api:3.0.2'

    api 'javax.inject:javax.inject:1'

    api 'net.java.dev.jna:jna:5.16.0'

    api 'org.antlr:antlr4:4.11.1'
    api 'org.antlr:antlr4-runtime:4.11.1'

    api 'org.apache.commons:commons-collections4:4.4'
    api 'org.apache.commons:commons-compress:1.27.1'
    api 'org.apache.commons:commons-lang3:3.17.0'
    api 'org.apache.commons:commons-text:1.13.0'

    api 'org.apache.maven:maven-artifact:3.9.9'

    api 'org.awaitility:awaitility:4.3.0'

    api 'org.bouncycastle:bcpkix-jdk18on:1.80'
    api 'org.bouncycastle:bcprov-jdk18on:1.80'

    api 'org.fusesource.jansi:jansi:2.4.1'

    api 'org.hibernate.validator:hibernate-validator:8.0.2.Final'

    api 'org.hyperledger.besu:besu-native-common:1.3.1'
    api 'org.hyperledger.besu:arithmetic:1.3.1'
    api 'org.hyperledger.besu:blake2bf:1.3.1'
    api 'org.hyperledger.besu:gnark:1.3.1'
    api 'org.hyperledger.besu:ipa-multipoint:1.3.1'
    api 'org.hyperledger.besu:secp256k1:1.3.1'
    api 'org.hyperledger.besu:secp256r1:1.3.1'

    api 'org.hyperledger.besu:besu-errorprone-checks:1.0.0'

    api 'org.jacoco:org.jacoco.agent:0.8.12'
    api 'org.jacoco:org.jacoco.core:0.8.12'

    api 'org.junit.platform:junit-platform-runner:1.12.0'

    api 'org.jupnp:org.jupnp:3.0.3'
    api 'org.jupnp:org.jupnp.support:3.0.3'

    api 'org.openjdk.jmh:jmh-core:1.37'
    api 'org.openjdk.jmh:jmh-generator-annprocess:1.37'

    api 'org.openjdk.jol:jol-core:0.17'

    api 'org.owasp.encoder:encoder:1.3.1'

    api 'org.rocksdb:rocksdbjni:9.7.3'

    api 'org.springframework.security:spring-security-crypto:6.4.4'

    api 'org.web3j:abi:4.12.3'
    api 'org.web3j:besu:4.12.3'
    api 'org.web3j:core:4.12.3'
    api 'org.web3j:crypto:4.12.3'
    api 'org.web3j:quorum:4.10.0'

    api 'org.xerial.snappy:snappy-java:********'

    api 'io.consensys.protocols:jc-kzg-4844:2.1.1'

    api 'tech.pegasys.discovery:discovery:25.4.0'
  }
}


spotless {
  // spotless check applied to build.gradle (groovy) files
  groovyGradle {
    target '*.gradle'
    greclipse('4.31').configFile(rootProject.file('gradle/spotless/greclipse.properties'))
    endWithNewline()
  }
}

publishing {
  publications {
    mavenJavaPlatform(MavenPublication) {
      from components.javaPlatform
      groupId "org.hyperledger.besu"
      artifactId 'bom'
      version calculateVersion()

      pom {
        name = "Besu BOM"
        url = 'http://github.com/hyperledger/besu'
        licenses {
          license {
            name = 'The Apache License, Version 2.0'
            url = 'http://www.apache.org/licenses/LICENSE-2.0.txt'
          }
        }
        scm {
          connection = 'scm:git:git://github.com/hyperledger/besu.git'
          developerConnection = 'scm:git:ssh://github.com/hyperledger/besu.git'
          url = 'https://github.com/hyperledger/besu'
        }
        withXml {
          // Workaround since Gradle does not natively support adding classifiers in BOM
          // https://github.com/gradle/gradle/issues/8561#issuecomment-467456299

          Node pomNode = asNode()
          Node dependencies = pomNode.dependencyManagement.dependencies.get(0)
          addDependency(dependencies, "org.hyperledger.besu.internal", "besu-ethereum-core", "test-support", calculateVersion())
        }
      }
    }
  }
}

def addDependency(parent, groupId, artifactId, classifier, version) {
  parent.appendNode('dependency')
    .appendNode('groupId', groupId).parent()
    .appendNode('artifactId', artifactId).parent()
    .appendNode('classifier', classifier).parent()
    .appendNode('version', version)
}
