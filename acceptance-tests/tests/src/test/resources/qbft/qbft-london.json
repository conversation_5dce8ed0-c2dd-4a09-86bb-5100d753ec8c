{"config": {"chainId": 4, "berlinBlock": 0, "londonBlock": 0, "qbft": {"blockperiodseconds": 1, "epochlength": 30000, "requesttimeoutseconds": 5, "blockreward": "5000000000000000000"}}, "nonce": "0x0", "timestamp": "0x58ee40ba", "extraData": "%extraData%", "gasLimit": "0x47b760", "difficulty": "0x1", "mixHash": "0x63746963616c2062797a616e74696e65206661756c7420746f6c6572616e6365", "coinbase": "0x0000000000000000000000000000000000000000", "alloc": {"fe3b557e8fb62b89f4916b721be55ceb828dbd73": {"privateKey": "8f2a55949038a9610f50fb23b5883af3b4ecb3c3bb792cbcefbd1542c692be63", "comment": "private key and this comment are ignored.  In a real chain, the private key should NOT be stored", "balance": "0xad78ebc5ac6200000"}, "627306090abaB3A6e1400e9345bC60c78a8BEf57": {"privateKey": "c87509a1c067bbde78beb793e6fa76530b6382a4c0241e5e4a9ec0a0f44dc0d3", "comment": "private key and this comment are ignored.  In a real chain, the private key should NOT be stored", "balance": "90000000000000000000000"}, "f17f52151EbEF6C7334FAD080c5704D77216b732": {"privateKey": "ae6ae8e5ccbfb04590405997ee2d52d2b330726137b875053c36d94e974d162f", "comment": "private key and this comment are ignored.  In a real chain, the private key should NOT be stored", "balance": "90000000000000000000000"}}, "number": "0x0", "gasUsed": "0x0", "parentHash": "0x0000000000000000000000000000000000000000000000000000000000000000"}