{"request": {"jsonrpc": "2.0", "method": "engine_newPayloadV4", "params": [{"parentHash": "0x9c93ae6e647584baeedfd4b53a1b17870797e9a7bd221f8e126a0cf11105064c", "feeRecipient": "******************************************", "stateRoot": "0x9ecda840c4b6b80bc4d1b483f9c5eb26b50f659cc74676c9f0c097dc774434f3", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "prevRandao": "0x0000000000000000000000000000000000000000000000000000000000000000", "gasLimit": "0x1ca35ef", "gasUsed": "0x0", "timestamp": "0x30", "extraData": "0x", "baseFeePerGas": "0x7", "excessBlobGas": "0x0", "blobGasUsed": "0x0", "transactions": [], "withdrawals": [], "blockNumber": "0x3", "receiptsRoot": "0x56e81f171bcc55a6ff8345e692c0f86e5b48e01b996cadc001622fb5e363b421", "blockHash": "0x92166cdc0fead978bcdc54ae9d9f2b803c65beb50350aa54baab12bb248b87cc"}, [], "0x0000000000000000000000000000000000000000000000000000000000000000", []], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "result": {"status": "VALID", "latestValidHash": "0x92166cdc0fead978bcdc54ae9d9f2b803c65beb50350aa54baab12bb248b87cc", "validationError": null}}, "statusCode": 200}