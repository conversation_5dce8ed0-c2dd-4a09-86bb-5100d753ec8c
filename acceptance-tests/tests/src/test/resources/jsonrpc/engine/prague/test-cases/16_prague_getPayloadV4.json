{"request": {"jsonrpc": "2.0", "method": "engine_getPayloadV4", "params": ["0x282643ec34a0cb41"], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "result": {"executionPayload": {"parentHash": "0x92166cdc0fead978bcdc54ae9d9f2b803c65beb50350aa54baab12bb248b87cc", "feeRecipient": "******************************************", "stateRoot": "0xc550dcaa4eb5f4235021dc905dc0d7d70ebcd019e9df1b4fa59dfbdd77debede", "logsBloom": "0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000100000000000000000004000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000200000000000000000000000008000000000000000000000000000000000000000800000000000000000000000000", "prevRandao": "0x0000000000000000000000000000000000000000000000000000000000000000", "gasLimit": "0x1caa87b", "gasUsed": "0x3b6c5", "timestamp": "0x40", "extraData": "0x", "baseFeePerGas": "0x7", "excessBlobGas": "0x0", "blobGasUsed": "0x0", "parentBeaconBlockRoot": "0x0000000000000000000000000000000000000000000000000000000000000000", "transactions": ["0xf8a08085e8d4a51000832dc6c09400a3ca265ebcb825b45f985a16cefb49958ce01702b8388706d19a62f28a6a6549f96c5adaebac9124a61d44868ec94f6d2d707c6a2f82c9162071231dfeb40e24bfde4ffdf2430000000000000000822fdfa00476c1a81f80f4c130acb5f8b8075468ba0893d766b7ec51a8d9723c573ad034a03bd3eaedabbaaf745f15023185ba66584ad3ee8bb40b9bef8c0b9ed27f8b1959", "0xf8c80185e8d4a51000832dc6c09400b42dbf2194e931e80326d950320f7d9dbeac0201b860fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe822fe0a05b88b593926d340f448918ef1c6263356c37f2434774e0fdb1cb9d90cfa5a23ba003a86aac4adb774181ba51eda17efb5fbed99ad57895e6eb56ccdf508a88a7cc"], "withdrawals": [], "blockNumber": "0x4", "receiptsRoot": "0x640f4036d53782ca4c5e9273ba6d657db4f5ff4fe526a8ed1997af9d0b8ae2d3", "blockHash": "0x2474414979bbc7467dd1382ff3562b4d1f5b26f03b76808c613a0c78871cce58"}, "blockValue": "0x360b8482c4b509d", "blobsBundle": {"commitments": [], "proofs": [], "blobs": []}, "shouldOverrideBuilder": false, "executionRequests": ["0x0123618e81e3f5cdf7f54c3d65f7fbc0abf5b21e8f8706d19a62f28a6a6549f96c5adaebac9124a61d44868ec94f6d2d707c6a2f82c9162071231dfeb40e24bfde4ffdf2430000000000000000", "0x0223618e81e3f5cdf7f54c3d65f7fbc0abf5b21e8ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffe"]}}, "statusCode": 200, "waitTime": 1500}