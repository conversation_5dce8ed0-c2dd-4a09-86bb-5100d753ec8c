{"request": {"jsonrpc": "2.0", "method": "engine_forkchoiceUpdatedV3", "params": [{"headBlockHash": "0x92166cdc0fead978bcdc54ae9d9f2b803c65beb50350aa54baab12bb248b87cc", "safeBlockHash": "0x92166cdc0fead978bcdc54ae9d9f2b803c65beb50350aa54baab12bb248b87cc", "finalizedBlockHash": "0x92166cdc0fead978bcdc54ae9d9f2b803c65beb50350aa54baab12bb248b87cc"}, {"timestamp": "0x40", "prevRandao": "0x0000000000000000000000000000000000000000000000000000000000000000", "suggestedFeeRecipient": "******************************************", "withdrawals": [], "parentBeaconBlockRoot": "0x0000000000000000000000000000000000000000000000000000000000000000"}], "id": 67}, "response": {"jsonrpc": "2.0", "id": 67, "result": {"payloadStatus": {"status": "VALID", "latestValidHash": "0x92166cdc0fead978bcdc54ae9d9f2b803c65beb50350aa54baab12bb248b87cc", "validationError": null}, "payloadId": "0x282643ec34a0cb41"}}, "statusCode": 200}