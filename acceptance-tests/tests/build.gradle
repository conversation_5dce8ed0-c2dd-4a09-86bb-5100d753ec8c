/*
 * Copyright 2019 ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

plugins {
  id 'org.web3j' version '4.14.0'
  id 'org.web3j.solidity' version '0.6.0'
}

web3j { generatedPackageName = 'org.hyperledger.besu.tests.web3j.generated' }

sourceSets.main.solidity.srcDirs = ["$projectDir/contracts"]

solidity {
  resolvePackages = false
  // TODO: remove the forced version, when DEV network is upgraded to support latest forks
  version '0.8.19'
  evmVersion 'london'
}

dependencies {
  api 'org.slf4j:slf4j-api'

  implementation project(':crypto:algorithms')
  implementation project(':ethereum:eth')

  testImplementation project(':acceptance-tests:dsl')
  testImplementation project(':acceptance-tests:test-plugins')
  testImplementation project(':app')
  testImplementation project(':config')
  testImplementation project(':consensus:clique')
  testImplementation project(':datatypes')
  testImplementation project(':ethereum:api')
  testImplementation project(':ethereum:core')
  testImplementation project(path: ':ethereum:core', configuration: 'testSupportArtifacts')
  testImplementation project(':ethereum:eth')
  testImplementation project(':ethereum:p2p')
  testImplementation project(':ethereum:permissioning')
  testImplementation project(':ethereum:rlp')
  testImplementation project(':metrics:core')
  testImplementation project(':plugin-api')
  testImplementation project(':testutil')
  testImplementation project(':util')
  implementation project(':plugins:rocksdb')

  testImplementation 'commons-io:commons-io'
  testImplementation 'io.grpc:grpc-all'
  testImplementation 'io.grpc:grpc-core'
  testImplementation 'io.grpc:grpc-netty'
  testImplementation 'io.grpc:grpc-stub'
  testImplementation 'io.opentelemetry:opentelemetry-extension-trace-propagators'
  testImplementation 'io.opentelemetry.instrumentation:opentelemetry-okhttp-3.0'
  testImplementation 'io.netty:netty-all'
  testImplementation 'io.opentelemetry:opentelemetry-api'
  testImplementation 'io.opentelemetry:opentelemetry-exporter-otlp'
  testImplementation 'io.opentelemetry.proto:opentelemetry-proto'
  testImplementation 'io.opentelemetry:opentelemetry-sdk'
  testImplementation 'io.opentelemetry:opentelemetry-sdk-trace'
  testImplementation 'io.opentracing.contrib:opentracing-okhttp3'
  testImplementation 'io.opentracing:opentracing-api'
  testImplementation 'io.opentracing:opentracing-util'
  testImplementation 'io.vertx:vertx-core'
  testImplementation 'org.apache.commons:commons-compress'
  testImplementation 'org.apache.logging.log4j:log4j-core'
  testImplementation 'io.consensys.tuweni:tuweni-crypto'
  testImplementation 'org.assertj:assertj-core'
  testImplementation 'org.awaitility:awaitility'
  testImplementation 'org.junit.jupiter:junit-jupiter'
  testImplementation 'org.web3j:abi'
  testImplementation 'org.web3j:besu'
  testImplementation 'org.web3j:core'
  testImplementation 'com.google.dagger:dagger'
  testAnnotationProcessor 'com.google.dagger:dagger-compiler'
  testImplementation project(path: ':acceptance-tests:tests:shanghai')
}

test.enabled = false

sourceSets {
  test {
    resources {
      srcDirs "${rootDir}/acceptance-tests/test-plugins/build/libs"
    }
  }
}

processTestResources.dependsOn(':acceptance-tests:test-plugins:testPluginsJar',':acceptance-tests:test-plugins:jar')

// FKA acceptanceTestNotPrivacy
task acceptanceTest(type: Test) {
  inputs.property "integration.date", LocalTime.now() // so it runs at every invocation
  exclude '**/bftsoak/**'
  exclude '**/acceptanceqbft/**'

  useJUnitPlatform {}

  dependsOn(rootProject.installDist)
  setSystemProperties(test.getSystemProperties())
  systemProperty 'acctests.runBesuAsProcess', 'true'
  systemProperty 'java.security.properties', "${buildDir}/resources/test/acceptanceTesting.security"
  def javaProjects = rootProject.subprojects - project(':platform')
  mustRunAfter javaProjects.test
  description = 'Runs ALL Besu acceptance tests (excluding bftsoak and acceptanceqbft).'
  group = 'verification'

  jvmArgs "-XX:ErrorFile=${buildDir}/jvmErrorLogs/java_err_pid%p.log"

  testLogging {
    exceptionFormat = 'full'
    showStackTraces = true
    showStandardStreams = Boolean.getBoolean('acctests.showStandardStreams')
    showExceptions = true
    showCauses = true
  }

  doFirst { mkdir "${buildDir}/jvmErrorLogs" }
}

task acceptanceTestBftSoak(type: Test) {
  inputs.property "integration.date", LocalTime.now() // so it runs at every invocation
  include '**/bftsoak/**'

  useJUnitPlatform {}

  dependsOn(rootProject.installDist)
  setSystemProperties(test.getSystemProperties())
  systemProperty 'acctests.runBesuAsProcess', 'true'
  // Set to any time > 60 minutes to run the soak test for longer
  // systemProperty 'acctests.soakTimeMins', '120'
  systemProperty 'java.security.properties', "${buildDir}/resources/test/acceptanceTesting.security"
  def javaProjects = rootProject.subprojects - project(':platform')
  mustRunAfter javaProjects.test
  description = 'Runs BFT soak test.'
  group = 'verification'

  jvmArgs "-XX:ErrorFile=${buildDir}/jvmErrorLogs/java_err_pid%p.log"

  testLogging {
    exceptionFormat = 'full'
    showStackTraces = true
    showStandardStreams = true
    showExceptions = true
    showCauses = true
  }

  doFirst { mkdir "${buildDir}/jvmErrorLogs" }
}

task acceptanceQbftTest(type: Test) {
  inputs.property "integration.date", LocalTime.now() // so it runs at every invocation
  include '**/acceptanceqbft/**'

  useJUnitPlatform {}

  dependsOn(rootProject.installDist)
  setSystemProperties(test.getSystemProperties())
  systemProperty 'acctests.runBesuAsProcess', 'true'
  systemProperty 'java.security.properties', "${buildDir}/resources/test/acceptanceTesting.security"
  def javaProjects = rootProject.subprojects - project(':platform')
  mustRunAfter javaProjects.test
  description = 'Runs Besu acceptance tests using QBFT nodes.'
  group = 'verification'

  jvmArgs "-XX:ErrorFile=${buildDir}/jvmErrorLogs/java_err_pid%p.log"

  testLogging {
    exceptionFormat = 'full'
    showStackTraces = true
    showStandardStreams = Boolean.getBoolean('acctests.showStandardStreams')
    showExceptions = true
    showCauses = true
  }

  doFirst { mkdir "${buildDir}/jvmErrorLogs" }
}
