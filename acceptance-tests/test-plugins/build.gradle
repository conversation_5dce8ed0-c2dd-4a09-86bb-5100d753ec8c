
dependencies {
  api 'org.slf4j:slf4j-api'

  implementation project(':app')
  implementation project(':crypto:algorithms')
  implementation project(':datatypes')
  implementation project(':ethereum:core')
  implementation project(':ethereum:rlp')
  implementation project(':ethereum:api')
  implementation project(':plugin-api')
  implementation 'com.google.auto.service:auto-service'
  implementation 'info.picocli:picocli'

  testImplementation 'org.assertj:assertj-core'
  testImplementation 'org.junit.jupiter:junit-jupiter'
}

task testPluginsJar(type: Jar) {
  archiveFileName = 'testPlugins.jar'
  manifest {
    attributes(
      'Specification-Title': archiveBaseName,
      'Specification-Version': project.version,
      'Implementation-Title': archiveBaseName,
      'Implementation-Version': calculateVersion(),
      'Commit-Hash': getGitCommitDetails(40).hash
      )
  }
  from sourceSets.main.output
}

artifacts { testPluginsJar }


javadoc { enabled = false }
