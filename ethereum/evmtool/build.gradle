/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 */

plugins {
  id 'org.graalvm.buildtools.native' version '0.10.6'
}

apply plugin: 'java-library'
apply plugin: 'application'
apply plugin: 'idea'

jar {
  archiveBaseName = calculateArtifactId(project)
  manifest {
    attributes(
      'Specification-Title': archiveBaseName,
      'Specification-Version': project.version,
      'Implementation-Title': archiveBaseName,
      'Implementation-Version': calculateVersion(),
      'Commit-Hash': getGitCommitDetails(40).hash
      )
  }
}

dependencies {

  implementation project(':app')
  implementation project(':config')
  implementation project(':crypto:algorithms')
  implementation project(':datatypes')
  implementation project(':ethereum:api')
  implementation project(':ethereum:core')
  implementation project(':ethereum:referencetests')
  implementation project(':ethereum:rlp')
  implementation project(':evm')
  implementation project(':metrics:core')
  implementation project(':plugins:rocksdb')
  implementation project(':services:kvstore')
  implementation project(':util')

  implementation 'com.fasterxml.jackson.core:jackson-databind'
  implementation 'com.google.dagger:dagger'
  implementation 'com.google.guava:guava'
  implementation 'info.picocli:picocli'
  implementation 'io.vertx:vertx-core'
  implementation 'tools.profiler:async-profiler:4.0'
  implementation 'com.github.oshi:oshi-core:6.6.5'

  annotationProcessor 'com.google.dagger:dagger-compiler'
  annotationProcessor 'info.picocli:picocli-codegen'

  testImplementation 'org.assertj:assertj-core'
  testImplementation 'org.junit.jupiter:junit-jupiter'
  testImplementation 'org.mockito:mockito-core'
  testImplementation 'org.mockito:mockito-junit-jupiter'

  // No logging in graalvm EvmTool
  nativeImageClasspath 'org.slf4j:slf4j-nop'
}

application {
  mainClass = 'org.hyperledger.besu.evmtool.EvmTool'
}


// rename the top level dir from besu-<version> to besu and this makes it really
// simple for use in docker
tasks.register("dockerDistUntar") {
  dependsOn distTar
  dependsOn distZip
  def dockerBuildDir = "${buildDir}/docker-besu-evmtool"
  def distTarFile = distTar.outputs.files.singleFile
  def distTarFileName = distTar.outputs.files.singleFile.name.replace(".tar", "")

  doFirst {
    new File(dockerBuildDir).mkdir()
    copy {
      from tarTree(distTarFile)
      into(dockerBuildDir)
    }
    file("${dockerBuildDir}/${distTarFileName}").renameTo("${dockerBuildDir}/besu-evmtool")
  }
}

tasks.register('distDocker', Exec) {
  dependsOn dockerDistUntar
  def dockerBuildVersion = project.hasProperty('release.releaseVersion') ? project.property('release.releaseVersion') : "${rootProject.version}"
  def dockerOrgName = project.hasProperty('dockerOrgName') ? project.getProperty("dockerOrgName") : "hyperledger"
  def dockerArtifactName = project.hasProperty("dockerArtifactName") ? "${project.getProperty("dockerArtifactName")}-evmtool" : "besu-evmtool"
  def imageName = "${dockerOrgName}/${dockerArtifactName}"

  def image = "${imageName}:${dockerBuildVersion}"
  def dockerBuildDir = "${buildDir}/docker-besu-evmtool/"
  workingDir "${dockerBuildDir}"

  doFirst {
    copy {
      from file("${projectDir}/src/main/docker/Dockerfile")
      into(workingDir)
    }
  }

  def gitDetails = getGitCommitDetails(10)
  executable "sh"
  args "-c", "docker build --build-arg BUILD_DATE=${buildTime()} --build-arg VERSION=${dockerBuildVersion} --build-arg VCS_REF=${gitDetails.hash} -t ${image} ."
}

tasks.register('dockerUpload', Exec) {
  dependsOn distDocker
  String dockerBuildVersion = project.hasProperty('release.releaseVersion') ? project.property('release.releaseVersion') : "${rootProject.version}"
  def dockerOrgName = project.hasProperty('dockerOrgName') ? project.getProperty("dockerOrgName") : "hyperledger"
  def dockerArtifactName = project.hasProperty("dockerArtifactName") ? "${project.getProperty("dockerArtifactName")}-evmtool" : "besu-evmtool"
  def imageName = "${dockerOrgName}/${dockerArtifactName}"
  def image = "${imageName}:${dockerBuildVersion}"
  def cmd = "docker push '${image}'"
  def additionalTags = []

  if (project.hasProperty('branch') && project.property('branch') == 'main') {
    additionalTags.add('develop')
  }

  if (!(dockerBuildVersion ==~ /.*-SNAPSHOT/ || dockerBuildVersion ==~/.*develop.*/)) {
    additionalTags.add('latest')
    additionalTags.add(dockerBuildVersion.split(/\./)[0..1].join('.'))
  }

  additionalTags.each { tag -> cmd += " && docker tag '${image}' '${imageName}:${tag.trim()}' && docker push '${imageName}:${tag.trim()}'" }
  executable "sh"
  args "-c", cmd
}
