<?xml version="1.0" encoding="UTF-8"?>
<verification-metadata xmlns="https://schema.gradle.org/dependency-verification" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="https://schema.gradle.org/dependency-verification https://schema.gradle.org/dependency-verification/dependency-verification-1.3.xsd">
   <configuration>
      <verify-metadata>true</verify-metadata>
      <verify-signatures>false</verify-signatures>
      <trusted-artifacts>
         <trust file=".*-javadoc[.]jar" regex="true"/>
         <trust file=".*-sources[.]jar" regex="true"/>
      </trusted-artifacts>
   </configuration>
   <components>
      <component group="aopalliance" name="aopalliance" version="1.0">
         <artifact name="aopalliance-1.0.jar">
            <sha256 value="0addec670fedcd3f113c5c8091d783280d23f75e3acb841b61a9cdb079376a08" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="aopalliance-1.0.pom">
            <sha256 value="26e82330157d6b844b67a8064945e206581e772977183e3e31fec6058aa9a59b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.diffplug.durian" name="durian-collect" version="1.2.0">
         <artifact name="durian-collect-1.2.0.jar">
            <sha256 value="b194c0b88021cc116c21c1dc76f49c2c1fe175af5bcb74c8ba67b9dbbf9a48cc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="durian-collect-1.2.0.pom">
            <sha256 value="8bb762086a0a4fd2a6473bbf905c744763afa1d59a5630f73bb04b7872c09ff3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.diffplug.durian" name="durian-core" version="1.2.0">
         <artifact name="durian-core-1.2.0.jar">
            <sha256 value="17ed0aacb3a3c16323332168bbdead8694f32800b2b47d69d4a4c49b114d5dae" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="durian-core-1.2.0.pom">
            <sha256 value="870320e90755371b01796fe81ba525fd1dee8b70346f554551eedd4289f0b662" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.diffplug.durian" name="durian-io" version="1.2.0">
         <artifact name="durian-io-1.2.0.jar">
            <sha256 value="095fd1dc77888c073f0be39a018156ee526722798b09de9e285ef2135e16eac4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="durian-io-1.2.0.pom">
            <sha256 value="350919424324e2750a3ddc2fa1bce6a90ac8ce2925698a60a9b4d1d6e4922ffe" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.diffplug.durian" name="durian-swt.os" version="4.3.0">
         <artifact name="durian-swt.os-4.3.0.jar">
            <sha256 value="81e2b639a7e4be6dc9b7245713cf06f5cab51f29db2e16b9b57645c96fde2884" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="durian-swt.os-4.3.0.module">
            <sha256 value="20536a95f2feb2bf4305128c6aaecb6fd89dc45798a9c85f2602b8a809d750db" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.diffplug.durian" name="durian-swt.os" version="4.3.1">
         <artifact name="durian-swt.os-4.3.1.jar">
            <sha256 value="8fcf4f4fecb6c0b4f1ef8de4c979bd5bcfb886b689b48556cbf33cf181ad4464" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="durian-swt.os-4.3.1.module">
            <sha256 value="ca6530b9821a92aee5645c5f5ed12809feb99add228f1a5a0270051e1b308358" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.diffplug.spotless" name="com.diffplug.spotless.gradle.plugin" version="7.0.3">
         <artifact name="com.diffplug.spotless.gradle.plugin-7.0.3.pom">
            <sha256 value="1b6e48d509b0e56ea5aa6e60fd6ae35add4958eee2e94385b7a8205524bd0f2e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.diffplug.spotless" name="spotless-lib" version="3.1.1">
         <artifact name="spotless-lib-3.1.1.jar">
            <sha256 value="dec2727ca7d617100a23a445941fdfa295800084c68de7ccf5a3c7b6e7330691" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="spotless-lib-3.1.1.module">
            <sha256 value="864a5ef929af12c4a46d178ced6268e9233d81ca249019eec005677ecf1f1e77" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.diffplug.spotless" name="spotless-lib-extra" version="3.1.1">
         <artifact name="spotless-lib-extra-3.1.1.jar">
            <sha256 value="7cddd57ad0506b1e54ed0a71bad337ffeb9f1b40ce29458c7b18838e9e297b2d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="spotless-lib-extra-3.1.1.module">
            <sha256 value="a77979240c507730c61d2d3eef165a10ef4e5ee6a52195846aaaa71757486bc3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.diffplug.spotless" name="spotless-plugin-gradle" version="7.0.3">
         <artifact name="spotless-plugin-gradle-7.0.3.jar">
            <sha256 value="b299804f65a1fcb9ae7757fd6e2e36988691f25702af86bd5606b6bafe5206c8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="spotless-plugin-gradle-7.0.3.module">
            <sha256 value="8d131348dded1a059e26e70560d1e437074108fb49dd7df5c2037714c5a5fd8b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml" name="classmate" version="1.5.1">
         <artifact name="classmate-1.5.1.jar">
            <sha256 value="aab4de3006808c09d25dd4ff4a3611cfb63c95463cfd99e73d2e1680d229a33b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="classmate-1.5.1.pom">
            <sha256 value="24de66a0029fddc259594c7bc7c5ae046a222c35bf367c739a07297599b51fae" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml" name="oss-parent" version="35">
         <artifact name="oss-parent-35.pom">
            <sha256 value="afc05ed21064eacac8de3002530cb192208bd114eb248417dad1886cbf54056e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml" name="oss-parent" version="48">
         <artifact name="oss-parent-48.pom">
            <sha256 value="11bba22d8631816e09b623a200747453d6491a66eac8f5c089c73da2b749014f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml" name="oss-parent" version="56">
         <artifact name="oss-parent-56.pom">
            <sha256 value="fd491f78857424106d2e3d605bcd799b53d31a565cdc868463ca7e875db45a50" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml" name="oss-parent" version="58">
         <artifact name="oss-parent-58.pom">
            <sha256 value="5670e6ac1c4ddcc9d413cf87997a5da2efaa4d2abe439363af9ef102a0a09e40" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml" name="oss-parent" version="61">
         <artifact name="oss-parent-61.pom">
            <sha256 value="3649513cf597e9186da0855986a8c543e12bdbd805edeef9c124db56dd036544" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-base" version="2.14.1">
         <artifact name="jackson-base-2.14.1.pom">
            <sha256 value="18015d1bacba9a14625a8bf19411f5bfad82d0060df37b27bd02e78132c4676e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-base" version="2.16.1">
         <artifact name="jackson-base-2.16.1.pom">
            <sha256 value="8e2385161197c9fe12dc9965b4c8b57f3d103217512205b5b60b9dc675bcea8d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-base" version="2.17.1">
         <artifact name="jackson-base-2.17.1.pom">
            <sha256 value="e0aefc61d38fcddd955ffeec01bb75104f1bbfffa3a6ecb58dbe74afb715e322" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-base" version="2.18.2">
         <artifact name="jackson-base-2.18.2.pom">
            <sha256 value="ef574b72f5b48948044f68376b874c88ae09a029dc8e05f64a1c30bdfb6de14a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-bom" version="2.14.1">
         <artifact name="jackson-bom-2.14.1.pom">
            <sha256 value="78fdf99e5050fc485f4117dab8cccbfb6fa6c68385eb5f38a09b655371d4a6cc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-bom" version="2.16.1">
         <artifact name="jackson-bom-2.16.1.pom">
            <sha256 value="69d8bf9b2a7d42c9cf1d70ad82be42f6ac6fd788918a6e1d75792ecdcc117a0b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-bom" version="2.17.1">
         <artifact name="jackson-bom-2.17.1.pom">
            <sha256 value="9f4461228e129103eed7a302dc1001ab2e4c82dd3cea915c2a7dbb8cc61efd25" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-bom" version="2.17.2">
         <artifact name="jackson-bom-2.17.2.pom">
            <sha256 value="1f472b0bc2004d5cf421ac48871417f84189e78f35c049718387fd8b44fb9f32" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-bom" version="2.18.2">
         <artifact name="jackson-bom-2.18.2.pom">
            <sha256 value="5247cdc301725d3f67f7ef049037289d5af709d6972bcf4b17096e4548d2956b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-parent" version="2.14">
         <artifact name="jackson-parent-2.14.pom">
            <sha256 value="0906add855ae39f92357d63f485889b08fca4c43a5fe433522d75344e0757b19" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-parent" version="2.16">
         <artifact name="jackson-parent-2.16.pom">
            <sha256 value="8bf6142812148a2abf6850b272f0af4c3d8ff1121ed604c402f3f38c9e6546ab" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-parent" version="2.17">
         <artifact name="jackson-parent-2.17.pom">
            <sha256 value="aee6de4a97283b040e43f4dad575e7b74796cd984d89276f7ec7567380c8a29d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson" name="jackson-parent" version="2.18.1">
         <artifact name="jackson-parent-2.18.1.pom">
            <sha256 value="d0822fac1a0226844b8ad445c920c49a4f619169d09b1010a7dfeed19910998c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.core" name="jackson-annotations" version="2.14.1">
         <artifact name="jackson-annotations-2.14.1.jar">
            <sha256 value="d255b4b863ff8ec714a8f96fa55c34621d43dbb82b82d3f57476496a4c09e1e7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-annotations-2.14.1.module">
            <sha256 value="267a680bb72cbd752c76b79eb908ae0c0abeb114fcb1c20a9678f0378898b9eb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.core" name="jackson-annotations" version="2.17.1">
         <artifact name="jackson-annotations-2.17.1.jar">
            <sha256 value="fccad82e13172c0e4384db71577219c9b8631c0820f4b18daaa57016fb661c76" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-annotations-2.17.1.module">
            <sha256 value="54d4d51da013a696be09e1f48031f7f40b77718078aa936e64c023a4fd9b9593" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.core" name="jackson-annotations" version="2.18.2">
         <artifact name="jackson-annotations-2.18.2.jar">
            <sha256 value="581bd61000ef7648943f781ca05689e56d03f6052748365a8e2b3a9b5d3fa32f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-annotations-2.18.2.module">
            <sha256 value="e11baf9b536e6df94daa635a1013ec3e069b866b8e112ddc2aa0446a155050dc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.core" name="jackson-core" version="2.14.1">
         <artifact name="jackson-core-2.14.1.jar">
            <sha256 value="0114187e296b34c931c1bf9e5a84152b62bfab7d182f5623f3982dc2da35e526" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-core-2.14.1.module">
            <sha256 value="7c8b8035f900f3f1cbdb06b8c79dc2b18b11f6afa13b0b74719cee349ff4c329" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.core" name="jackson-core" version="2.16.1">
         <artifact name="jackson-core-2.16.1.module">
            <sha256 value="4ba080b7b79a231a09bfb0284c2c94449eceba6433265591c1ed820d6073a6e2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.core" name="jackson-core" version="2.17.1">
         <artifact name="jackson-core-2.17.1.jar">
            <sha256 value="ddb26c8a1f1a84535e8213c48b35b253370434e3287b3cf15777856fc4e58ce6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-core-2.17.1.module">
            <sha256 value="8a8c0964fdfc26cedbb28d825df4621817fb8cd22b9e7a59d9c74a3a5f1bdd1d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.core" name="jackson-core" version="2.18.2">
         <artifact name="jackson-core-2.18.2.jar">
            <sha256 value="d8054ae7c0d1c2d2f55d28e46026ebe5892881f3fab5f439233184381c3b4a1f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-core-2.18.2.module">
            <sha256 value="ca78c60436767fcc36ce146b774e4f50a9cb9f632d13172c44bae88e0c03cfa2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.core" name="jackson-databind" version="2.14.1">
         <artifact name="jackson-databind-2.14.1.jar">
            <sha256 value="423a0c806de4b3fa5eb4a28698305e3a3777c731e1bcfa1b2f3a3760c7b6e773" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-databind-2.14.1.module">
            <sha256 value="d817977c8a6b0aaeda519fb2a7b8dcba02b38c3c272024fada32c5cce7e3d3cb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.core" name="jackson-databind" version="2.17.1">
         <artifact name="jackson-databind-2.17.1.jar">
            <sha256 value="b6ca2f7d5b1ab245cec5495ec339773d2d90554c48592590673fb18f4400a948" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-databind-2.17.1.module">
            <sha256 value="0babc646a062f0d9d3584402a50271884edc3e17a41942c1e31e0e10d71dbee6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.core" name="jackson-databind" version="2.18.2">
         <artifact name="jackson-databind-2.18.2.jar">
            <sha256 value="4b364e6850dc89172fcf1d4dd26b8ff5488eda44ff4657e22dd265203dd5ab3c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-databind-2.18.2.module">
            <sha256 value="8c7dac2f727818d8847a82aa4eac6b0174d79cf04df90de2246072e6dd34e700" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.dataformat" name="jackson-dataformat-yaml" version="2.18.2">
         <artifact name="jackson-dataformat-yaml-2.18.2.jar">
            <sha256 value="381a1c0711e4bb88561a6c0008b5a945465628ca07764ccd66a0d97ee07ad612" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-dataformat-yaml-2.18.2.module">
            <sha256 value="7afc664172c3a6e6c6c3584765a0329dc6feabbfe6bba89baead8bd2e9fbec7b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.dataformat" name="jackson-dataformats-text" version="2.18.2">
         <artifact name="jackson-dataformats-text-2.18.2.pom">
            <sha256 value="e21d5d88b0474a11b71fe94100c4f5295bfa174f2ee6ae4b0ad02b7616478648" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.datatype" name="jackson-datatype-jdk8" version="2.16.1">
         <artifact name="jackson-datatype-jdk8-2.16.1.module">
            <sha256 value="8b1501f4ce7af47c1498765504fce15e169476e609af50e7c69c662cc3e4f992" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.datatype" name="jackson-datatype-jdk8" version="2.18.2">
         <artifact name="jackson-datatype-jdk8-2.18.2.jar">
            <sha256 value="f30d77f5826b9e9813342e84ab412095ed4ed5cf4fef6f93cebb848cb0fd0294" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-datatype-jdk8-2.18.2.module">
            <sha256 value="41186f28498077581d54a7e11f2747afabb7be4969db5b62c04b1fb89b34b000" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jackson-datatype-jdk8-2.18.2.pom">
            <sha256 value="65de3229ba8eb699d94834fc75ddd2ede964e8ba76cbb17f565536602728222a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.module" name="jackson-modules-java8" version="2.16.1">
         <artifact name="jackson-modules-java8-2.16.1.pom">
            <sha256 value="188722535859efcae5ab75c3062dc32e0dde8e1eaa6c1821f59052c4203c44a5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.fasterxml.jackson.module" name="jackson-modules-java8" version="2.18.2">
         <artifact name="jackson-modules-java8-2.18.2.pom">
            <sha256 value="b3acfb910d023e93a4199afccde1ff9ec5fab0c550dc4f968a5057117acb0b36" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.ben-manes.caffeine" name="caffeine" version="3.2.0">
         <artifact name="caffeine-3.2.0.jar">
            <sha256 value="ec411dfdf0c03f25218648ce89861630b71680e5858a9a7278ebac8e55cab3d7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="caffeine-3.2.0.module">
            <sha256 value="be293342ddaf54d3bf66d811050285dd889cfadf3ac253c1f13ce5131794f9a4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="caffeine-3.2.0.pom">
            <sha256 value="bf418ab677a31782502229a8fb35bf573f88a36678ec076d6e9337d383e5eae6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jk1" name="gradle-license-report" version="2.9">
         <artifact name="gradle-license-report-2.9.jar">
            <sha256 value="ebfd6da851654c53216eea9eda1485c12e0cd6de5a9919bf5da9735a021f32af" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="gradle-license-report-2.9.module">
            <sha256 value="4139a508481c369ae0f2627fa8387f1e20e58600f2037cdc1cdaa164e056f235" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jk1.dependency-license-report" name="com.github.jk1.dependency-license-report.gradle.plugin" version="2.9">
         <artifact name="com.github.jk1.dependency-license-report.gradle.plugin-2.9.pom">
            <sha256 value="a79ca4dfe069d737faf075c8f4b6c6471c2e5cea8e1546946ae333d747fddf02" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jnr" name="jffi" version="1.3.11">
         <artifact name="jffi-1.3.11.pom">
            <sha256 value="10e5dbb018060229d991afe9bfda7b8a5b339bb6f6fdaf244642fc4f67ea7214" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jnr" name="jffi" version="1.3.13">
         <artifact name="jffi-1.3.13-native.jar">
            <sha256 value="a38dd049951a27f7a423389fecf0195a5bd414f418e90f5ac9992652c22a5b8e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jffi-1.3.13.jar">
            <sha256 value="78df5fb05d7e2541b867bedc538b18840245a601bb2160fa26824bb67ed93878" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jffi-1.3.13.pom">
            <sha256 value="3053e9ce46e6b682665ad112f3f69cb494f9b297f1c4a83969c1220b7525924f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jnr" name="jnr-a64asm" version="1.0.0">
         <artifact name="jnr-a64asm-1.0.0.jar">
            <sha256 value="53ae5ea7fa5c284e8279aa348e7b9de4548b0cae10bfd058fa217c791875e4cf" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jnr-a64asm-1.0.0.pom">
            <sha256 value="9dd9c299ca3ec9287db2ec26571a5cc611dc21a1a4f5d2ef1255a924face47a8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jnr" name="jnr-constants" version="0.10.4">
         <artifact name="jnr-constants-0.10.4.jar">
            <sha256 value="9a5b8cf9798d9d0331b8d8966c5235a22c4307676e35803a24659e6d76096f78" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jnr-constants-0.10.4.pom">
            <sha256 value="e2fb974cad142b021e700a50cd7a4e908f02e4f3b356cc0abbe93d8ab49774b5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jnr" name="jnr-enxio" version="0.32.17">
         <artifact name="jnr-enxio-0.32.17.jar">
            <sha256 value="f97f9479e3ce270e3b8520d9d6831fd45d1dcb85bb9195402c0454a852c349c5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jnr-enxio-0.32.17.pom">
            <sha256 value="5f31fbbc2ea84500e5f1b61c56860d97967f8de355eb04a32c64e19c805ca18b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jnr" name="jnr-ffi" version="2.2.14">
         <artifact name="jnr-ffi-2.2.14.pom">
            <sha256 value="f7e38a0f6b0abaa62a3a2760bb1db2538ee226cecf16d4ac45f6df9cf11a6ae5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jnr" name="jnr-ffi" version="2.2.16">
         <artifact name="jnr-ffi-2.2.16.jar">
            <sha256 value="fdb7fae8c8be666734e236642e41e6434cf1519d4c8029d03c9a4e019f17ebc0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jnr-ffi-2.2.16.pom">
            <sha256 value="3d50d5b8b0f910fb8ddec550345bce0eef42ec8dd73c535b4fcc9c5bf84b60b5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jnr" name="jnr-posix" version="3.1.19">
         <artifact name="jnr-posix-3.1.19.jar">
            <sha256 value="2d0a2567c3c34a6d7960d0970ec0b26bfd567bb242972e0fad5166957c6714cc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jnr-posix-3.1.19.pom">
            <sha256 value="70608e244e88d06f2e8104954e39cec6a9ab3e478fecbb84845cff6750fc8bad" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jnr" name="jnr-unixsocket" version="0.38.22">
         <artifact name="jnr-unixsocket-0.38.22.jar">
            <sha256 value="394babd88a3d55e099a54cd9bea7ba825f0d56a9ca1c3538291f18b2e45b845f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jnr-unixsocket-0.38.22.pom">
            <sha256 value="299dde36ecb0cded7570d6f09f7e48ba50f90f88787685283bfd3d1a4d2877cc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.jnr" name="jnr-x86asm" version="1.0.2">
         <artifact name="jnr-x86asm-1.0.2.jar">
            <sha256 value="39f3675b910e6e9b93825f8284bec9f4ad3044cd20a6f7c8ff9e2f8695ebf21e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jnr-x86asm-1.0.2.pom">
            <sha256 value="ea862ce3bd96ccb8ca36b8e9e7baef2da3fbbfbdc256baeaa8c8a873911074e7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.kevinstern" name="software-and-algorithms" version="1.0">
         <artifact name="software-and-algorithms-1.0.jar">
            <sha256 value="61ab82439cef37343b14f53154c461619375373a56b9338e895709fb54e0864c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="software-and-algorithms-1.0.pom">
            <sha256 value="ed971f009354e68c09414c964fbe1a2c85c2c8bbd460122b7497a7b8c5d4f388" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.node-gradle" name="gradle-node-plugin" version="7.0.2">
         <artifact name="gradle-node-plugin-7.0.2.jar">
            <sha256 value="bfd8cb644593e7f6b6b719011fdaca4af0c02485e28176904470f300e48a19e2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="gradle-node-plugin-7.0.2.module">
            <sha256 value="1b62e71ff2acfdc1e1172247f857be677874db98d336f4d629e7e455c04b33c0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.openjson" name="openjson" version="1.0.13">
         <artifact name="openjson-1.0.13.jar">
            <sha256 value="7d4184cf891f359ee72095a4ce1cb9cd8f3c676676dfe716ea89fab4d0e6e953" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="openjson-1.0.13.pom">
            <sha256 value="6c7e953b4f5ab7df94a699ee9ba8e4ecd9de2a084b72899b57647a23758986a8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.oshi" name="oshi-core" version="6.6.5">
         <artifact name="oshi-core-6.6.5.pom">
            <sha256 value="72165a862e3f953eff72e0da01839e3db8d2ff65b60b293b0e9a65fe54e87893" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.oshi" name="oshi-core" version="6.6.6">
         <artifact name="oshi-core-6.6.6.jar">
            <sha256 value="94ed084848c5e086a386bc8113fb3a228c36cb1416a43d956d9f2bb55c71de4b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="oshi-core-6.6.6.pom">
            <sha256 value="bb37a57b888fcea81e8270d56d1ed5f6e9f660e70b563946c00900c11ddcc1a8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.oshi" name="oshi-parent" version="6.6.5">
         <artifact name="oshi-parent-6.6.5.pom">
            <sha256 value="2a3b73eaf780bbc67370a2b5c27aeef2737af54284f73bce6deee139ca734473" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.oshi" name="oshi-parent" version="6.6.6">
         <artifact name="oshi-parent-6.6.6.pom">
            <sha256 value="196434d32caa45e76bae9a591b98940f0a6b82d99055a198ab280c4d53d3a87b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.spotbugs" name="spotbugs-annotations" version="4.8.1">
         <artifact name="spotbugs-annotations-4.8.1.jar">
            <sha256 value="06eba41a81aaccb011c3f75afa019e509cda7f1eb7a4e057bb860c60845f915e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="spotbugs-annotations-4.8.1.module">
            <sha256 value="a28eb2cf74c59c3dc976d69d0259f4030abe5f52fabc193378bd52e3f8813dd3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.github.zafarkhaja" name="java-semver" version="0.10.2">
         <artifact name="java-semver-0.10.2.jar">
            <sha256 value="a8e32e17575d0188c1f3e2e57bb95db09793883c8853fde828b63ecb1f99a63a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="java-semver-0.10.2.pom">
            <sha256 value="7f005ff3f900e86955f5a534b5a9aa8ea54bb5d60bb60ac23759550a36676835" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google" name="google" version="5">
         <artifact name="google-5.pom">
            <sha256 value="e09d345e73ca3fbca7f3e05f30deb74e9d39dd6b79a93fee8c511f23417b6828" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.android" name="annotations" version="4.1.1.4">
         <artifact name="annotations-4.1.1.4.jar">
            <sha256 value="ba734e1e84c09d615af6a09d33034b4f0442f8772dec120efb376d86a565ae15" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="annotations-4.1.1.4.pom">
            <sha256 value="e4bb54753c36a27a0e5d70154a5034fedd8feac4282295034bfd483d6c7aae78" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.api.grpc" name="proto-google-common-protos" version="2.48.0">
         <artifact name="proto-google-common-protos-2.48.0.jar">
            <sha256 value="43ec7807459aaa4012e838a1be4ef2d590cf233305da60af5b54f08ec8cf2302" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="proto-google-common-protos-2.48.0.pom">
            <sha256 value="45eb927b22685fc11bfab69ce6ad3b439f72d816a3a7de46bd23f4ed19bf89c3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auth" name="google-auth-library-credentials" version="1.24.1">
         <artifact name="google-auth-library-credentials-1.24.1.jar">
            <sha256 value="5dbf1207d14e093f67995f457cb69c3cf49bed1364150b23465e09acada65d96" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="google-auth-library-credentials-1.24.1.pom">
            <sha256 value="60a5b661fdc251f09b8b2b4dd5c24b3f1d2c0688a18660ec0e2d86a8b30ba1f6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auth" name="google-auth-library-oauth2-http" version="1.24.1">
         <artifact name="google-auth-library-oauth2-http-1.24.1.jar">
            <sha256 value="88a75cd4448ea2f3b46e48a89497a6cf0985a5fa4e21274af4940e07f59f6eaf" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="google-auth-library-oauth2-http-1.24.1.pom">
            <sha256 value="cc5c9e5fac9335d119df7e337d0552b51f4a4074553267cbd745c72b33d8a635" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auth" name="google-auth-library-parent" version="1.24.1">
         <artifact name="google-auth-library-parent-1.24.1.pom">
            <sha256 value="120c24b8eb39768abd732d16a7af6ff92b9dc9c19b7f848e29ee807ab0a52ebe" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auto" name="auto-common" version="1.2.1">
         <artifact name="auto-common-1.2.1.jar">
            <sha256 value="f43f29fe2a6ebaf04b2598cdeec32a4e346d49a9404e990f5fc19c19f3a28d0e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="auto-common-1.2.1.pom">
            <sha256 value="13b4b50062949f8b1343927c581536d3bb051b8afea509aa6f902f4de28bc1b2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auto" name="auto-common" version="1.2.2">
         <artifact name="auto-common-1.2.2.jar">
            <sha256 value="f50b1ce8a41fad31a8a819c052f8ffa362ea0a3dbe9ef8f7c7dc9a36d4738a59" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="auto-common-1.2.2.pom">
            <sha256 value="03a992fba4fcfbbcf77a460981181630046e5bae09673a48193c6e0f761271ea" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auto.service" name="auto-service" version="1.1.1">
         <artifact name="auto-service-1.1.1.jar">
            <sha256 value="1f48f451503e623daba7d9ed368cca0f81e1e3815653a4560113e12c0129ebd5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="auto-service-1.1.1.pom">
            <sha256 value="0c53c2f87949b3be8f1c8c67bb76c08ed4f4beb8dbaa475c32ba593d46176317" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auto.service" name="auto-service-aggregator" version="1.0.1">
         <artifact name="auto-service-aggregator-1.0.1.pom">
            <sha256 value="0481460db59903362b26fabd3bdcd5ec200750f9391bbb6502fe3c7dc2df3e4c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auto.service" name="auto-service-aggregator" version="1.1.1">
         <artifact name="auto-service-aggregator-1.1.1.pom">
            <sha256 value="e2dc3e263a5db0cc8172fb2eb9bcd232acecfdc96e817103eab3c8d9f1900c6a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auto.service" name="auto-service-annotations" version="1.0.1">
         <artifact name="auto-service-annotations-1.0.1.pom">
            <sha256 value="e9bbf1b3d66ca18004a6a07a20dbfa10ca2ce8367348f741de2fe8fefce68cc2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auto.service" name="auto-service-annotations" version="1.1.1">
         <artifact name="auto-service-annotations-1.1.1.jar">
            <sha256 value="16a76dd00a2650568447f5d6e3a9e2c809d9a42367d56b45215cfb89731f4d24" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="auto-service-annotations-1.1.1.pom">
            <sha256 value="1659fd0a25cea9fa9abfb18deb9d7b180cce3aceab16ff9e625461567e44a2df" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auto.value" name="auto-value-annotations" version="1.11.0">
         <artifact name="auto-value-annotations-1.11.0.jar">
            <sha256 value="5a055ce4255333b3346e1a8703da5bf8ff049532286fdcd31712d624abe111dd" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="auto-value-annotations-1.11.0.pom">
            <sha256 value="2aec16e34ea3e011621a0322f4f36f8f9bf988bb54462b5570976e89ea07b122" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auto.value" name="auto-value-annotations" version="1.9">
         <artifact name="auto-value-annotations-1.9.jar">
            <sha256 value="fa5469f4c44ee598a2d8f033ab0a9dcbc6498a0c5e0c998dfa0c2adf51358044" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="auto-value-annotations-1.9.pom">
            <sha256 value="9220a3dabe75c79334f061b0e40df833448666bac28a8b823bc1e8fd52f8c98a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auto.value" name="auto-value-parent" version="1.11.0">
         <artifact name="auto-value-parent-1.11.0.pom">
            <sha256 value="5a0d1d718552e8a45dcce0128d1b6b9623fa971a82cd245753233ba7208cb29d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.auto.value" name="auto-value-parent" version="1.9">
         <artifact name="auto-value-parent-1.9.pom">
            <sha256 value="2e3d9dff639601cab881d7e170806bcbd8e031f7ebff259cbb45be57b4cbd787" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.cloud" name="google-cloud-shared-config" version="1.9.1">
         <artifact name="google-cloud-shared-config-1.9.1.pom">
            <sha256 value="f5591de20569b95ce2f8d6101b7a3b54be6c7c4b4d54a6bc939ffba31575e302" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.cloud" name="native-image-shared-config" version="1.7.6">
         <artifact name="native-image-shared-config-1.7.6.pom">
            <sha256 value="b99a24469710fdec8effe6cbf1fffb6e1609b1ca1b8b5d3e9b796cdbb90eb93f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.cloud" name="native-image-shared-config" version="1.9.1">
         <artifact name="native-image-shared-config-1.9.1.pom">
            <sha256 value="f6ac8de0ac90bd1825b3f3338f1f8d3255c73c1902ec3de8427e336b48b21f03" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.cloud.opentelemetry" name="detector-resources-support" version="0.33.0">
         <artifact name="detector-resources-support-0.33.0.jar">
            <sha256 value="94b0def27754083ceaa67b56a4d483d294e9f17066493df3ef7e81ec5c3bb2c0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="detector-resources-support-0.33.0.module">
            <sha256 value="5bf360731a5164435f732b46d2bb0ea922407e596dcab9362080c233ce74e5ad" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.code.findbugs" name="jsr305" version="3.0.2">
         <artifact name="jsr305-3.0.2.jar">
            <sha256 value="766ad2a0783f2687962c8ad74ceecc38a28b9f72a2d085ee438b7813e928d0c7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jsr305-3.0.2.pom">
            <sha256 value="19889dbdf1b254b2601a5ee645b8147a974644882297684c798afe5d63d78dfe" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.code.gson" name="gson" version="2.11.0">
         <artifact name="gson-2.11.0.jar">
            <sha256 value="57928d6e5a6edeb2abd3770a8f95ba44dce45f3b23b7a9dc2b309c581552a78b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="gson-2.11.0.pom">
            <sha256 value="c0e547bea998888e6e25c5886a90e762272bc88b5275343dd2c05ded6ca2e360" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.code.gson" name="gson" version="2.9.0">
         <artifact name="gson-2.9.0.pom">
            <sha256 value="7190d0b07f278e9f4c603f44e543940f81cf1a2559f851c6f298c9bb2be2978c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.code.gson" name="gson-parent" version="2.11.0">
         <artifact name="gson-parent-2.11.0.pom">
            <sha256 value="8acb1f3b72a6f026916ac0735bad9aab0293d527edb7b365327def13a9367b7a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.code.gson" name="gson-parent" version="2.9.0">
         <artifact name="gson-parent-2.9.0.pom">
            <sha256 value="af781c9a5766ffea311a0df0536576a64decc661aa110c4de5c73ac8bf434424" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.dagger" name="dagger" version="2.55">
         <artifact name="dagger-2.55.jar">
            <sha256 value="adc6d9d27e33f8589bcfd3d9ddffc740c85cec3cf4e61b92aef8aa80100f08c0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="dagger-2.55.pom">
            <sha256 value="d17d162fb35c80a641ed62c0d56fedf2eb41cc5d06436c64e45a7ad82038fcda" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.dagger" name="dagger-compiler" version="2.55">
         <artifact name="dagger-compiler-2.55.jar">
            <sha256 value="11f1e98acc379eebbc574252d89195431a1564240bfc29e0c152021138ad6eb5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="dagger-compiler-2.55.pom">
            <sha256 value="28b52c8a12be79d7782bd356f1d47be5ad5c0f6b72532328f168a5a93504336f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.dagger" name="dagger-spi" version="2.55">
         <artifact name="dagger-spi-2.55.jar">
            <sha256 value="ab0c8ed833fd6ede58146685a52aeff01c6a1a9232be99e77047a0163f147d5f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="dagger-spi-2.55.pom">
            <sha256 value="af98178d0f0237dc5a0ca5ef65795a7931deeb80e55446f6c533bf2be8533584" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.devtools.ksp" name="symbol-processing-api" version="2.0.21-1.0.28">
         <artifact name="symbol-processing-api-2.0.21-1.0.28.jar">
            <sha256 value="e38dd8199c65aae4bce9929bf39a563bf7658c600be5066c7e042cdd8de3137c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="symbol-processing-api-2.0.21-1.0.28.module">
            <sha256 value="8324a417e092ca2728732edb8281ee91ad0861c1c6c694a4dd2e9d4e79f4e270" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_annotation" version="2.25.0">
         <artifact name="error_prone_annotation-2.25.0.jar">
            <sha256 value="57bb89bb4e316503cb30e30f35b35b3c8c083ab3ffd05f368f1860f3b0b46cf9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="error_prone_annotation-2.25.0.pom">
            <sha256 value="efd58f6848c1805740d481ba919d455a2924ce7164927cbab7a83de6e26646d0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_annotations" version="2.21.1">
         <artifact name="error_prone_annotations-2.21.1.jar">
            <sha256 value="d1f3c66aa91ac52549e00ae3b208ba4b9af7d72d68f230643553beb38e6118ac" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="error_prone_annotations-2.21.1.pom">
            <sha256 value="f598880feefaea9d674dc41db13ab37004bf03776b5bb21c04dede8e920c1f12" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_annotations" version="2.25.0">
         <artifact name="error_prone_annotations-2.25.0.pom">
            <sha256 value="b58d79dee1968d58dce9c5b03e42106d7dd4c07f7078eb393c671e58f23bf611" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_annotations" version="2.27.0">
         <artifact name="error_prone_annotations-2.27.0.jar">
            <sha256 value="24c923372c58e35d0b9f16a028929bb9aedc77521867c274f2bd0735df5ba1f5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="error_prone_annotations-2.27.0.pom">
            <sha256 value="4ca5a35d61235e16549ac346d1e34551cdf0fe27e84aa57e03cbeb255ea4e5da" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_annotations" version="2.30.0">
         <artifact name="error_prone_annotations-2.30.0.pom">
            <sha256 value="f713849c23b34953e854565dce8aa795cae0c1416611b720c21461322c795f86" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_annotations" version="2.36.0">
         <artifact name="error_prone_annotations-2.36.0.jar">
            <sha256 value="77440e270b0bc9a249903c5a076c36a722c4886ca4f42675f2903a1c53ed61a5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="error_prone_annotations-2.36.0.pom">
            <sha256 value="d79cfd37c85f76d6b754c7501ee1dc8447b7b2642c2382d778252166bd331e9c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_check_api" version="2.25.0">
         <artifact name="error_prone_check_api-2.25.0.jar">
            <sha256 value="37e5991c45235a491584226c2bad6ef881fcaf117692264a45da84b4c10cdd04" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="error_prone_check_api-2.25.0.pom">
            <sha256 value="1abc7d1074d6d2b8bd3566901f3cd1e2fd136b0e90d7535ee6d3e21d9d252837" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_core" version="2.25.0">
         <artifact name="error_prone_core-2.25.0.jar">
            <sha256 value="94eb07120db11ca0138c15ada352fab138a4e7bf853bb91da676128c91888449" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="error_prone_core-2.25.0.pom">
            <sha256 value="9484087f6a15933cabe7013c834142d9726e647628192a412b4c7d50ecccaf56" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_parent" version="2.21.1">
         <artifact name="error_prone_parent-2.21.1.pom">
            <sha256 value="32bb0b5ff241fd6ba1feea448aebb9cedef1699be73cb6f319365387b82bf92c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_parent" version="2.25.0">
         <artifact name="error_prone_parent-2.25.0.pom">
            <sha256 value="56753de4bea7a20c29d699667f29f29727c0bbad5cf69973a994094718da75c5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_parent" version="2.27.0">
         <artifact name="error_prone_parent-2.27.0.pom">
            <sha256 value="fa81829d049559df6927f9c9a6fd6bbd09f8b50e6d4736ae72c8300b6c3d7654" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_parent" version="2.30.0">
         <artifact name="error_prone_parent-2.30.0.pom">
            <sha256 value="5e8834ccc0e5ed0c72f306c250b518e6ad10d075a9b7b910cf695cba1ee02a2d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_parent" version="2.36.0">
         <artifact name="error_prone_parent-2.36.0.pom">
            <sha256 value="3a4cfc8a6bed61eb48e969796fc31ea0d270b63e670599946a61883adb7094dc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="error_prone_type_annotations" version="2.25.0">
         <artifact name="error_prone_type_annotations-2.25.0.jar">
            <sha256 value="448563e05b4dff032c261dfcffc373df5c208921a83ccf0caf6b314db9370ae3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="error_prone_type_annotations-2.25.0.pom">
            <sha256 value="0d4c1b6496c283a6a9fd7cd10db36fb50f9935b0c12720ea10f2baf7597b08b0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="javac" version="9+181-r4173-1">
         <artifact name="javac-9+181-r4173-1.jar">
            <sha256 value="1d8d347a0e1579f3fc86ac04d1974e489afc66357f0009ac9804a7ac30912ed6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="javac-9+181-r4173-1.pom">
            <sha256 value="1569c081fa8358a06164372f9f541b8a0ea5ec0964931f5f74ead36ff52b4015" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.errorprone" name="javac-shaded" version="9-dev-r4023-3">
         <artifact name="javac-shaded-9-dev-r4023-3.jar">
            <sha256 value="65bfccf60986c47fbc17c9ebab0be626afc41741e0a6ec7109e0768817a36f30" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="javac-shaded-9-dev-r4023-3.pom">
            <sha256 value="7459fd63c1e73770ca44d37a7a685b731a946eb7cd701ccb284dcb0ce6de3f88" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.googlejavaformat" name="google-java-format" version="1.24.0">
         <artifact name="google-java-format-1.24.0.jar">
            <sha256 value="9a4e0b9f4ec4d71a8a1d3641fd481118100fda2eeab712dbdfd4b2a06e9de4ce" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="google-java-format-1.24.0.pom">
            <sha256 value="62412d788677ca12f1ca8d75b429b79fde0058b7ced4fb0d1ddd3485d3968482" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.googlejavaformat" name="google-java-format" version="1.25.2">
         <artifact name="google-java-format-1.25.2.jar">
            <sha256 value="938d0321c3aadc8e45a51e4334f61ca9f71137908e0fe8853925ee2125e98c6f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="google-java-format-1.25.2.pom">
            <sha256 value="59685c6e06b70bae2c596dc50660e4a9c32c9700eb8475da3cfcc9a5589d4704" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.googlejavaformat" name="google-java-format" version="1.5">
         <artifact name="google-java-format-1.5.jar">
            <sha256 value="aa19ad7850fb85178aa22f2fddb163b84d6ce4d0035872f30d4408195ca1144e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="google-java-format-1.5.pom">
            <sha256 value="994510ba3b16fb02e5ca17e8fd6158e2702fe95a1359c80be547b86b76a7aad5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.googlejavaformat" name="google-java-format-parent" version="1.24.0">
         <artifact name="google-java-format-parent-1.24.0.pom">
            <sha256 value="ad33197ed5eb9f04514e3f1cdfc1c31652af0a93c085413a53789f0bda9351dc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.googlejavaformat" name="google-java-format-parent" version="1.25.2">
         <artifact name="google-java-format-parent-1.25.2.pom">
            <sha256 value="25500efa1ef711ed9137d90906b133868ba068beec48af94090bf14c12969066" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.googlejavaformat" name="google-java-format-parent" version="1.5">
         <artifact name="google-java-format-parent-1.5.pom">
            <sha256 value="13aaf29158343f8b9c7dd7d3f58610290b05ad29ea69c7b9504869e47fbf6319" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="failureaccess" version="1.0.1">
         <artifact name="failureaccess-1.0.1.jar">
            <sha256 value="a171ee4c734dd2da837e4b16be9df4661afab72a41adaf31eb84dfdaf936ca26" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="failureaccess-1.0.1.pom">
            <sha256 value="e96042ce78fecba0da2be964522947c87b40a291b5fd3cd672a434924103c4b9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="failureaccess" version="1.0.2">
         <artifact name="failureaccess-1.0.2.jar">
            <sha256 value="8a8f81cf9b359e3f6dfa691a1e776985c061ef2f223c9b2c80753e1b458e8064" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="failureaccess-1.0.2.pom">
            <sha256 value="19ebc6f4bdb4edbb3d07b6ee994f846b54ef295582a9b5634719ffa9f31d03b2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava" version="32.1.2-jre">
         <artifact name="guava-32.1.2-jre.module">
            <sha256 value="e40cf085ced05ab18f9c94e7c7bc197e1cdb695bc939afc344ab24c1414d6c7e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava" version="32.1.3-jre">
         <artifact name="guava-32.1.3-jre.jar">
            <sha256 value="6d4e2b5a118aab62e6e5e29d185a0224eed82c85c40ac3d33cf04a270c3b3744" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="guava-32.1.3-jre.module">
            <sha256 value="f5fff7642c12e7627bc14289fd267e2602c17f9590e23522c3e63107f61c2942" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava" version="33.0.0-jre">
         <artifact name="guava-33.0.0-jre.module">
            <sha256 value="59a2dbd055d1baa762e78f1a5ba3ab973edd13fc27dcc1871105e2f797f682d3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava" version="33.3.1-android">
         <artifact name="guava-33.3.1-android.module">
            <sha256 value="6971614ddeee003e54d2b69c6b0c94ce8a5b5eba980d16455c81df9089a7acb7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava" version="33.3.1-jre">
         <artifact name="guava-33.3.1-jre.module">
            <sha256 value="41858c84753fd96a6b7c51122fccef39558c91cc08264e08506bcf20e0e63733" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava" version="33.4.0-jre">
         <artifact name="guava-33.4.0-jre.jar">
            <sha256 value="b918c98a7e44dbe94ebd9fe3e40cddaadb5a93e6a78eb6008b42df237241e538" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="guava-33.4.0-jre.module">
            <sha256 value="820e817e86c493aa7affd6cbb991ee60925b6c8b74541f742cb22070f6f20459" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="guava-33.4.0-jre.pom">
            <sha256 value="fa94db40022ddfc775af9ecfb130cce515b96f740daf82f20af846d95054134b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava-parent" version="26.0-android">
         <artifact name="guava-parent-26.0-android.pom">
            <sha256 value="f8698ab46ca996ce889c1afc8ca4f25eb8ac6b034dc898d4583742360016cc04" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava-parent" version="32.1.2-jre">
         <artifact name="guava-parent-32.1.2-jre.pom">
            <sha256 value="88e9cb007335ab5fdb314a6e3c987734ec2308c9a063ff747c7469189d0a92bf" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava-parent" version="32.1.3-jre">
         <artifact name="guava-parent-32.1.3-jre.pom">
            <sha256 value="f283c1f04897a9a88a3fa4ff46804e65e82114809a09cd04094bf7de01b1857b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava-parent" version="33.0.0-jre">
         <artifact name="guava-parent-33.0.0-jre.pom">
            <sha256 value="040cc88c680b413d70ba9fc8371b36093021b10996aa3598621de767d418229a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava-parent" version="33.3.1-android">
         <artifact name="guava-parent-33.3.1-android.pom">
            <sha256 value="6e11986ea7250b51f847157e2dc937f32a306804dfce0007a5e81ddb9b95c579" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava-parent" version="33.3.1-jre">
         <artifact name="guava-parent-33.3.1-jre.pom">
            <sha256 value="55441db27e8869dfefe053059bdf478bdc7e95585642bf391f0023345fd56287" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="guava-parent" version="33.4.0-jre">
         <artifact name="guava-parent-33.4.0-jre.pom">
            <sha256 value="3a499ed34a0d9ee0f1bcc39230021a1cd4e2f7dd0426ab6844f585465d41dcd7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.guava" name="listenablefuture" version="9999.0-empty-to-avoid-conflict-with-guava">
         <artifact name="listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar">
            <sha256 value="b372a037d4230aa57fbeffdef30fd6123f9c0c2db85d0aced00c91b974f33f99" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.pom">
            <sha256 value="18d4b1db26153d4e55079ce1f76bb1fe05cdb862ef9954a88cbcc4ff38b8679b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.http-client" name="google-http-client" version="1.44.2">
         <artifact name="google-http-client-1.44.2.jar">
            <sha256 value="390618d7b51704240b8fd28e1230fa35d220f93f4b4ba80f63e38db00dacb09e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="google-http-client-1.44.2.pom">
            <sha256 value="02cb052035517aff19403a84b9edf362b698d7eca81f0e323c7d454d7347dbac" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.http-client" name="google-http-client-bom" version="1.44.2">
         <artifact name="google-http-client-bom-1.44.2.pom">
            <sha256 value="214e5dd9e6274cad8f48583749550a4d639a706ca3756232c2d18b7703e26ec2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.http-client" name="google-http-client-gson" version="1.44.2">
         <artifact name="google-http-client-gson-1.44.2.jar">
            <sha256 value="1119b66685195310375b717de2215d6c5d14fa8ed9f57e07b4fecd461e7b9db7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="google-http-client-gson-1.44.2.pom">
            <sha256 value="e52e1cfd9c3ba87586d31ddb79d39a8daff7c6796e07c63e6dcee840d8bc7733" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.http-client" name="google-http-client-parent" version="1.44.2">
         <artifact name="google-http-client-parent-1.44.2.pom">
            <sha256 value="dd64c630b1b30e01b0548cdd2e4babd146fe4e09df785935acefbc26f2d95b9f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.inject" name="guice" version="5.1.0">
         <artifact name="guice-5.1.0.jar">
            <sha256 value="4130e50bfac48099c860f0d903b91860c81a249c90f38245f8fed58fc817bc26" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="guice-5.1.0.pom">
            <sha256 value="b3b8dc65213d623fb70ed7958dbdd616324256ba836a31652560c388999fd9cd" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.inject" name="guice-parent" version="5.1.0">
         <artifact name="guice-parent-5.1.0.pom">
            <sha256 value="63d5c0c641797deba6c051c47d5f6923f3a103ef71e4ebc3a85d01c3e878596c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.j2objc" name="j2objc-annotations" version="3.0.0">
         <artifact name="j2objc-annotations-3.0.0.jar">
            <sha256 value="88241573467ddca44ffd4d74aa04c2bbfd11bf7c17e0c342c94c9de7a70a7c64" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="j2objc-annotations-3.0.0.pom">
            <sha256 value="23b3d039e168ad89dd114698e6dd7be383f4a2c577b8877d82c73a6515e74a17" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.protobuf" name="protobuf-bom" version="3.25.5">
         <artifact name="protobuf-bom-3.25.5.pom">
            <sha256 value="080e2984173238b50e064c226afffbb1b0233520295c790a7fd3d6ae4593f063" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.protobuf" name="protobuf-bom" version="4.28.3">
         <artifact name="protobuf-bom-4.28.3.pom">
            <sha256 value="be3baa9b418864b5ed7d39c3beb3141d83fa78d124554eaa92083243dcbfb27a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.protobuf" name="protobuf-java" version="3.25.5">
         <artifact name="protobuf-java-3.25.5.jar">
            <sha256 value="8540247fad9e06baefa8fb45eb313802d019f485f14300e0f9d6b556ed88e753" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="protobuf-java-3.25.5.pom">
            <sha256 value="e752032157a7a39be9be3786684075452a46cd586b2865abd33e707568a4c8af" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.protobuf" name="protobuf-java" version="4.28.3">
         <artifact name="protobuf-java-4.28.3.jar">
            <sha256 value="ba02977c0fef8b40af9f85fe69af362d8e13f2685b49a9752750b18da726157e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="protobuf-java-4.28.3.pom">
            <sha256 value="f70bea74bd0b5325c2d2fc4d58d86163744019738d1ca436e067d733c5f7c125" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.protobuf" name="protobuf-java-util" version="3.25.5">
         <artifact name="protobuf-java-util-3.25.5.jar">
            <sha256 value="dacc58b2c3d2fa8d4bddc1acb881e78d6cf7c137dd78bc1d67f6aca732436a8d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="protobuf-java-util-3.25.5.pom">
            <sha256 value="a09d190eaa6a79616bc5f4b5404e94b0cab559803a98c8a090c4099962f41f92" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.protobuf" name="protobuf-parent" version="3.25.5">
         <artifact name="protobuf-parent-3.25.5.pom">
            <sha256 value="64cc0e3ad6e85f5aec8f9dcf9341d1379e9525364ff53e23e16d8d5824673ef7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.protobuf" name="protobuf-parent" version="4.28.3">
         <artifact name="protobuf-parent-4.28.3.pom">
            <sha256 value="5364cdc058b2e5a212af6dfc1894299070dd434026a15827bbe313774489f403" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.google.re2j" name="re2j" version="1.7">
         <artifact name="re2j-1.7.jar">
            <sha256 value="4f657af51ab8bb0909bcc3eb40862d26125af8cbcf92aaaba595fed77f947bc0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="re2j-1.7.pom">
            <sha256 value="8bb0b7ae941f572cc49c7d0315da477fcf701c59193c96a5a8fe2d59c0957a9a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.googlecode.concurrent-trees" name="concurrent-trees" version="2.6.1">
         <artifact name="concurrent-trees-2.6.1.jar">
            <sha256 value="04e3724984e2a5cbf55606cfa372a5bd3d3c5d2a21533a7004e3cde539761fa5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="concurrent-trees-2.6.1.pom">
            <sha256 value="43c2b9b142e7055d1f2a5827f10944925d22747d9756b3250c07aab47538a971" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.googlecode.javaewah" name="JavaEWAH" version="1.2.3">
         <artifact name="JavaEWAH-1.2.3.jar">
            <sha256 value="d65226949713c4c61a784f41c51167e7b0316f93764398ebba9e4336b3d954c2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="JavaEWAH-1.2.3.pom">
            <sha256 value="e4ed6c669620366f993920659fe0ac7cbc83d753dbc0dc0eb1e529973af96f23" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.graphql-java" name="graphql-java" version="22.3">
         <artifact name="graphql-java-22.3.jar">
            <sha256 value="8828fef5d8133d3d5ad23cee262a9b3ab4ce95aedf5e3332bb577a9aa7c627e0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="graphql-java-22.3.pom">
            <sha256 value="cfb2b4448115e686d150f3c9b572b4659e6619a7cb9c78bfa72c7acd36604cd9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.graphql-java" name="java-dataloader" version="3.3.0">
         <artifact name="java-dataloader-3.3.0.jar">
            <sha256 value="08cec84ac76e32b53ea666260f288f10b3731c21c89f9199b109ced2361f78b8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="java-dataloader-3.3.0.module">
            <sha256 value="2f37c9d74baf14c07db1b2668e38ee51e65a65a248e47307fbdb3ad97a83fcea" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="java-dataloader-3.3.0.pom">
            <sha256 value="a12dc5274f4f2d29b084d26e1259b56c7db8df2371de0b9dcf854c5ae5635860" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.ibm.icu" name="icu4j" version="72.1">
         <artifact name="icu4j-72.1.jar">
            <sha256 value="3df572b240a68d13b5cd778ad2393e885d26411434cd8f098ac5987ea2e64ce3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="icu4j-72.1.pom">
            <sha256 value="3def2b29af4a19ad805cb153056925a89a903f92fbee1adee12ed2fc14c44ee8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.ibm.icu" name="icu4j" version="74.2">
         <artifact name="icu4j-74.2.jar">
            <sha256 value="95c055080e14c093ebeeba5b733e1a1be7a4af5854668c774cedf070d4240e43" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="icu4j-74.2.pom">
            <sha256 value="ad5c4681cffa8362fa63462515586e0a39492e86eeb3f2d8d8db8d527585463e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.ibm.icu" name="icu4j-root" version="74.2">
         <artifact name="icu4j-root-74.2.pom">
            <sha256 value="a0daf2ab7b3d63b093ae76dd2fd9858df27508cee3b29f00b7dd6d25a1fde9d6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.jfrog.artifactory" name="com.jfrog.artifactory.gradle.plugin" version="5.2.5">
         <artifact name="com.jfrog.artifactory.gradle.plugin-5.2.5.pom">
            <sha256 value="f2a4e0ae2a7ecaeff18b89b1cff2cebda0c618ccee591d8e27efbbf36dfb3fb0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.splunk.logging" name="splunk-library-javalogging" version="1.11.8">
         <artifact name="splunk-library-javalogging-1.11.8.jar">
            <sha256 value="6cd3a7f05038d39d5e70b52d3f142a07abf5831fea6b3f77e539e8e245c521ff" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="splunk-library-javalogging-1.11.8.pom">
            <sha256 value="41305de1097acd1758524eba6eb04bae5becb143e8544626c53eb182f434f532" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup" name="javapoet" version="1.13.0">
         <artifact name="javapoet-1.13.0.jar">
            <sha256 value="4c7517e848a71b36d069d12bb3bf46a70fd4cda3105d822b0ed2e19c00b69291" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="javapoet-1.13.0.pom">
            <sha256 value="54a34fa8502a46bc90efdb49262600591fa80bf9a34f5a4c798311aec16ca977" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup" name="kotlinpoet" version="1.11.0">
         <artifact name="kotlinpoet-1.11.0.jar">
            <sha256 value="2887ada1ca03dd83baa2758640d87e840d1907564db0ef88d2289c868a980492" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlinpoet-1.11.0.module">
            <sha256 value="2d6ad09e7cacb9da63c6fdf333e3432b39942d5586336f02eb06f5b5450f34a0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup" name="kotlinpoet" version="1.16.0">
         <artifact name="kotlinpoet-1.16.0.module">
            <sha256 value="02f3dd33c384f4443896a6a3b07704ff56b12dfe5e32a10e7ea3266ae7894f53" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup" name="kotlinpoet-jvm" version="1.16.0">
         <artifact name="kotlinpoet-jvm-1.16.0.jar">
            <sha256 value="41a506824da430c0e543a023243eae88dba1888acf255b999a51da2dbefc0bc8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlinpoet-jvm-1.16.0.module">
            <sha256 value="a03d7315a4c6d56b4d519561e1f98f685a502f482294637a8c2ed37bf6fdd42d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup.okhttp3" name="logging-interceptor" version="4.12.0">
         <artifact name="logging-interceptor-4.12.0.jar">
            <sha256 value="f3e8d5f0903c250c2b55d2f47fcfe008e80634385da8385161c7a63aaed0c74c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="logging-interceptor-4.12.0.module">
            <sha256 value="2e2148cd213b4f8f54d66d0548a228057e1f7b67332e0f979b1a1ce36b2276a7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="logging-interceptor-4.12.0.pom">
            <sha256 value="31592487d5fa7ae5bcedf67806acbddc487dccf44e1fdb7fcf7ce5364acfc85c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup.okhttp3" name="okhttp" version="4.11.0">
         <artifact name="okhttp-4.11.0.module">
            <sha256 value="567c25b51d77796174439184d752412ba97ed9fdb65fc71840dbc5563beb8fa8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup.okhttp3" name="okhttp" version="4.12.0">
         <artifact name="okhttp-4.12.0.jar">
            <sha256 value="b1050081b14bb7a3a7e55a4d3ef01b5dcfabc453b4573a4fc019767191d5f4e0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="okhttp-4.12.0.module">
            <sha256 value="607e220ff8215b929d829bbf54f332894f1459b4d795979aeafcbcc1cea54cf3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="okhttp-4.12.0.pom">
            <sha256 value="7c737040a9419522e7c50cc027416a70fe7c7629e52b219935ade6b4119c7d38" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup.okhttp3" name="okhttp-urlconnection" version="4.12.0">
         <artifact name="okhttp-urlconnection-4.12.0.jar">
            <sha256 value="74c0e3f8c3df0b1d32ab9d839448c914586d3e8479611e4386fecfa6b3f0a26b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="okhttp-urlconnection-4.12.0.module">
            <sha256 value="a901efac8b81a6f9fe6c970708e353bd4bd1da03b929dfac01a7617b2d4fea31" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup.okio" name="okio" version="3.5.0">
         <artifact name="okio-3.5.0.module">
            <sha256 value="c5bd7a3db1413ea45d88c1943401d81c569a297c5c74e065f004f93c8a8b541e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup.okio" name="okio" version="3.6.0">
         <artifact name="okio-3.6.0.module">
            <sha256 value="6a47ac50364e6598459401fb86f9b6cfcdf637b9b3a3045b1cc33cbf4c408218" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup.okio" name="okio-jvm" version="3.5.0">
         <artifact name="okio-jvm-3.5.0.module">
            <sha256 value="a73e833eb63fed0e18f23f96b6341db3289a318fdece7563b0678156280e1392" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="com.squareup.okio" name="okio-jvm" version="3.6.0">
         <artifact name="okio-jvm-3.6.0.jar">
            <sha256 value="67543f0736fc422ae927ed0e504b98bc5e269fda0d3500579337cb713da28412" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="okio-jvm-3.6.0.module">
            <sha256 value="b1c2199e1c0cc969ef61cbbe4af2ecaf9b06411bdde01cbaf6fc9134dfe04e8a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="okio-jvm-3.6.0.pom">
            <sha256 value="61b4d7c515a0894ffad925fd7052620c1425a86433fd35113b5fab0de890a57f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-beanutils" name="commons-beanutils" version="1.9.4">
         <artifact name="commons-beanutils-1.9.4.jar">
            <sha256 value="7d938c81789028045c08c065e94be75fc280527620d5bd62b519d5838532368a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-beanutils-1.9.4.pom">
            <sha256 value="c35cca7b61d4678d9578cbc0b901b8717b539abf9254441da78b8fe60de064d0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-codec" name="commons-codec" version="1.16.0">
         <artifact name="commons-codec-1.16.0.jar">
            <sha256 value="56595fb20b0b85bc91d0d503dad50bb7f1b9afc0eed5dffa6cbb25929000484d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-codec-1.16.0.pom">
            <sha256 value="6cb5957819df393956fd311a3a0c3f5eec1ebc49ba5b2d09f3f44e6167fa3e74" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-codec" name="commons-codec" version="1.17.0">
         <artifact name="commons-codec-1.17.0.pom">
            <sha256 value="c01c4cda5e408f41ed1d83e4a0a170cf53801b6338aba49f0f904786bc1214fc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-codec" name="commons-codec" version="1.17.1">
         <artifact name="commons-codec-1.17.1.jar">
            <sha256 value="f9f6cb103f2ddc3c99a9d80ada2ae7bf0685111fd6bffccb72033d1da4e6ff23" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-codec-1.17.1.pom">
            <sha256 value="7fa0db4d8150daf932958b8aea89ee24abb4d18e2316a5de535278fc131512a0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-collections" name="commons-collections" version="3.2.2">
         <artifact name="commons-collections-3.2.2.jar">
            <sha256 value="eeeae917917144a68a741d4c0dff66aa5c5c5fd85593ff217bced3fc8ca783b8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-collections-3.2.2.pom">
            <sha256 value="d5d81fcc288c0d8c711c302007cada4aa9a226ed1a112d4baa64cb1d6322170b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-io" name="commons-io" version="2.11.0">
         <artifact name="commons-io-2.11.0.pom">
            <sha256 value="2e016fd7e3244b5f2c20acad834d93aa4790486ee1e4564641361a3e831eef59" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-io" name="commons-io" version="2.15.1">
         <artifact name="commons-io-2.15.1.jar">
            <sha256 value="a58af12ee1b68cfd2ebb0c27caef164f084381a00ec81a48cc275fd7ea54e154" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-io-2.15.1.pom">
            <sha256 value="171a1af82b6759eb5740b3b8809aca80113deaf1153036f2f4445901dfd3f91d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-io" name="commons-io" version="2.16.1">
         <artifact name="commons-io-2.16.1.jar">
            <sha256 value="f41f7baacd716896447ace9758621f62c1c6b0a91d89acee488da26fc477c84f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-io-2.16.1.pom">
            <sha256 value="5777d292251c7895c04a4c57015683ec3b353a12486c9b3e7178e9b0b3c38fff" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-io" name="commons-io" version="2.18.0">
         <artifact name="commons-io-2.18.0.jar">
            <sha256 value="f3ca0f8d63c40e23a56d54101c60d5edee136b42d84bfb85bc7963093109cf8b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-io-2.18.0.pom">
            <sha256 value="63d96941eb44df9c90d2adb2ad8c3f699c2e065e707045c5daf713fca52bcfca" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-jxpath" name="commons-jxpath" version="1.3">
         <artifact name="commons-jxpath-1.3.jar">
            <sha256 value="fcbc0ad917d9d6a73c6df21fac322e00d213ef19cd94815a007c407a8a3ff449" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-jxpath-1.3.pom">
            <sha256 value="7cc2188f02af72e03bc2a58571a047e1ee94b3e2050091165be75bfd50c9884d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-logging" name="commons-logging" version="1.2">
         <artifact name="commons-logging-1.2.jar">
            <sha256 value="daddea1ea0be0f56978ab3006b8ac92834afeefbd9b7e4e6316fca57df0fa636" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-logging-1.2.pom">
            <sha256 value="c91ab5aa570d86f6fd07cc158ec6bc2c50080402972ee9179fe24100739fbb20" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-logging" name="commons-logging" version="1.3.0">
         <artifact name="commons-logging-1.3.0.jar">
            <sha256 value="66d3c980470b99b0c511dad3dfc0ae7b265ec1fb144e96bc0253a8a175fd34d9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-logging-1.3.0.pom">
            <sha256 value="8defda7ceb4888ffe4d4e63278956a1a3c514b75b8363d6716a1bd66fe962c7f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-net" name="commons-net" version="3.11.1">
         <artifact name="commons-net-3.11.1.jar">
            <sha256 value="3bb861274992dba5487de328303745b7085de72694b63a3300be1e057144311e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-net-3.11.1.pom">
            <sha256 value="b1acdbcf78cab0205a68afb4b6f0bc3551e077a3bb2b3ff7cf9c000f2e9b834d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="commons-net" name="commons-net" version="3.9.0">
         <artifact name="commons-net-3.9.0.pom">
            <sha256 value="931b55cc96381620ea4ca19e563390b04ace42a2db577a3fcaab5c10364e615c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="dev.equo.ide" name="solstice" version="1.8.1">
         <artifact name="solstice-1.8.1.jar">
            <sha256 value="6e5ba2cce813be1d71ccdc2ecf3e49271b14e691bfbbb1a114cf3a30e773b10d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="solstice-1.8.1.module">
            <sha256 value="a676039ea6af08f257b46e07c2bf1571a46a4d4b5b9ccb86c3fc98d07fafea1b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="dnsjava" name="dnsjava" version="3.6.3">
         <artifact name="dnsjava-3.6.3.jar">
            <sha256 value="ce279b5acb753f58661756b6dcacdcc4dee76748cff04d2abf14edcc7e26a8de" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="dnsjava-3.6.3.pom">
            <sha256 value="417ae3f6c579fb0ba8895dccc533c232a1d55558ffe71608b708dd64d4007ebc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="ethereum" name="execution-spec-tests" version="4.5.0">
         <artifact name="execution-spec-tests-4.5.0-fixtures_develop.tar.gz">
            <sha256 value="58afb92a0075a2cb7c4dec1281f7cb88b21b02afbedad096b580f3f8cc14c54c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="info.picocli" name="picocli" version="4.7.6">
         <artifact name="picocli-4.7.6.jar">
            <sha256 value="ed441183f309b93f104ca9e071e314a4062a893184e18a3c7ad72ec9cba12ba0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="picocli-4.7.6.pom">
            <sha256 value="2d2248cccf487ad9ce208dec751745df15cd1e1924a4cc3d36841b5f1990c6fb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="info.picocli" name="picocli-codegen" version="4.7.6">
         <artifact name="picocli-codegen-4.7.6.jar">
            <sha256 value="245a2e5d7fc1f8c06b37b7aa0fc1781d47110cc7b4ec77dc3583cc505f99d827" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="picocli-codegen-4.7.6.pom">
            <sha256 value="b5e7f3167757bd6062f66b58be7f0d50e20e2d5db45507b9bd131f1bc750a345" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.protocols" name="jc-kzg-4844" version="2.0.0">
         <artifact name="jc-kzg-4844-2.0.0.jar">
            <sha256 value="354bbe0c5a7f03ea3b303a7eeeb530205b20d9d4c21d28be55f10520657ede29" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jc-kzg-4844-2.0.0.module">
            <sha256 value="3c23bb439b70581fc124a931d6d876e91d464b958cbebd7a1ad876498063ae69" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.protocols" name="jc-kzg-4844" version="2.1.1">
         <artifact name="jc-kzg-4844-2.1.1.jar">
            <sha256 value="4c3ba4f214f28ebc0c064fbc1a12dc72f1b8b806a5b30c3a1c30c2dc2ef8a421" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jc-kzg-4844-2.1.1.module">
            <sha256 value="778c2af9ef7cfb3d9d4dd93a4c4a2aa36bccb4f5d50a1205139a4dc388556762" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jc-kzg-4844-2.1.1.pom">
            <sha256 value="f19821ec3505b8944a15963593cfdfa7a7c9ccb487d15f2fa49eb84ed4a918c5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-bytes" version="2.7.0">
         <artifact name="tuweni-bytes-2.7.0.jar">
            <sha256 value="cd6834899ae959118eeb3890e8c7d5ba137506ae78bd37adffba21ccebae9c23" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuweni-bytes-2.7.0.pom">
            <sha256 value="5fdc3d593f86c8397b9ce0b8cdffc76641226ed46b9debb6fce5c6cc0c50be93" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-bytes" version="2.7.1">
         <artifact name="tuweni-bytes-2.7.1.jar">
            <sha256 value="56969ffbf7959ebfe4c422481a730b547221cfc47a85aebcf1c26ba15d0d21a1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuweni-bytes-2.7.1.pom">
            <sha256 value="98b2a551c925be00131b51f40ac820bd1e62179dd5d74324428c16d5c1a4b074" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-concurrent" version="2.7.1">
         <artifact name="tuweni-concurrent-2.7.1.jar">
            <sha256 value="af731ee8fc14a883ff9924f2e5894412e5f375db40b50cbd26fdadf693427175" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuweni-concurrent-2.7.1.pom">
            <sha256 value="bcb900a29090fe9bca496fdeb5fba86fbde888458ab0438e0401d55fc393352f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-config" version="2.7.1">
         <artifact name="tuweni-config-2.7.1.jar">
            <sha256 value="366157aa9467bca8ac2aec11878aea88e1a690755c15f77a3a0d00607915d62b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuweni-config-2.7.1.pom">
            <sha256 value="58fd902a50aa1c285a931c550bcddbf5b22ab6c92a86ca3d77618646438822b8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-crypto" version="2.7.0">
         <artifact name="tuweni-crypto-2.7.0.pom">
            <sha256 value="7ecd0c77aeb5fa23151e52e660b66631a32bd34a8d4798255c8f30150a51c2c5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-crypto" version="2.7.1">
         <artifact name="tuweni-crypto-2.7.1.jar">
            <sha256 value="c12eea059d44039ddfb73c3959fd813fd9e7be49cd0748ff24a3b2a5d30a4ca1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuweni-crypto-2.7.1.pom">
            <sha256 value="835fc12f5c93786b2d7bcfd6b4a66d2463731308899b89d9cdad5d49ae0d1326" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-io" version="2.7.1">
         <artifact name="tuweni-io-2.7.1.jar">
            <sha256 value="ff5056b0d45fbe3ce6eb5f2663cf35fac07c743e32e6998722ec7a5c817c32e8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuweni-io-2.7.1.pom">
            <sha256 value="8ef79b2752ec709481a0963447e28c201be689f20d87cdf796aed38bf7e91139" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-net" version="2.7.1">
         <artifact name="tuweni-net-2.7.1.jar">
            <sha256 value="3b28224f23018a37dc49b7d03e174deb8b4248ffc4d6dcb5cef892e4d06f3a0c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuweni-net-2.7.1.pom">
            <sha256 value="34e350c920757d80cc64501c15803bbed8a6b1235b8f6628de9667fbe2678489" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-rlp" version="2.7.0">
         <artifact name="tuweni-rlp-2.7.0.pom">
            <sha256 value="9c1778c38992d0a6db526462bb0889826cb25b435df5cab730a7c7c33fcf2e09" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-rlp" version="2.7.1">
         <artifact name="tuweni-rlp-2.7.1.jar">
            <sha256 value="4d8d980ab25a082fe22596ef53848de4902e91678cfe4079801133d40e101a2e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuweni-rlp-2.7.1.pom">
            <sha256 value="1d5e75a8aa38dfec045f1b5362104b8507314172b0ecb623630ad9da19180707" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-toml" version="2.7.1">
         <artifact name="tuweni-toml-2.7.1.jar">
            <sha256 value="00cfcc187ff46ce8d702a0f2cb98d6a4f2a67f8f41242b5901e46efdcb7c84b7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuweni-toml-2.7.1.pom">
            <sha256 value="5932c9d86300fcdf9b944b907e672c302a2843c661698f8e613dd41ddc99635c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-units" version="2.7.0">
         <artifact name="tuweni-units-2.7.0.jar">
            <sha256 value="b082b09e28a1cd38aa44c007f8c273c60408528a0bfb7559dd9523891bdc5f67" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuweni-units-2.7.0.pom">
            <sha256 value="9017f0f5609165b4e54a68c28af30fdd6e7a9bba71109f65b48b1e1b7445cfe2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.consensys.tuweni" name="tuweni-units" version="2.7.1">
         <artifact name="tuweni-units-2.7.1.jar">
            <sha256 value="2146dca428746ccf7b3b76732d9d8e74f5705c06570127e2f8efd015c58269c6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuweni-units-2.7.1.pom">
            <sha256 value="b2a93a25ed7ea6d8cc249157217cdd17c0c61f46ff19a4bae3f2bbe4ff56a158" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.fabric8" name="kubernetes-client-bom" version="5.12.4">
         <artifact name="kubernetes-client-bom-5.12.4.pom">
            <sha256 value="d232392a89c3efdc85ab299c5a94e8b9dd35bc02f340ce8446ff659aa15913a0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.github.adraffy" name="ens-normalize" version="0.2.0">
         <artifact name="ens-normalize-0.2.0.jar">
            <sha256 value="1855285d7203bbd413ea55ea6b75eeaa53113107158235fdb4d3a1a00bb4d750" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="ens-normalize-0.2.0.module">
            <sha256 value="c5b8c70b41fe1fa90759fc2f2a7a008dcf336892d208eacc82dcb5c247bc029a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="ens-normalize-0.2.0.pom">
            <sha256 value="c9f1ce2d98f9bc13fb3df6277e1ea70d91e313b2c3c7aafddc0ad575c3ba7de0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.github.eisop" name="dataflow-errorprone" version="3.41.0-eisop1">
         <artifact name="dataflow-errorprone-3.41.0-eisop1.jar">
            <sha256 value="10434fba4e53f55fa9c76904cde414b918932548c9dfc4e2d634ac05ff7a7d10" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="dataflow-errorprone-3.41.0-eisop1.pom">
            <sha256 value="23f1cdfa4613c00cc6ea5c9f5360e1471b449f82633f13cf5ff6cdb34fa31ebd" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.github.hakky54" name="sslcontext-kickstart" version="8.3.6">
         <artifact name="sslcontext-kickstart-8.3.6.jar">
            <sha256 value="e5fa688a7026676f970933838f603523347d84047d21e7b7bfecf422b32f27f0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="sslcontext-kickstart-8.3.6.pom">
            <sha256 value="00ccee0446ca3c2af1764cd73f68e22241b2dfd4f27872e38a2f0805f89a7dbe" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.github.hakky54" name="sslcontext-kickstart-bom" version="8.3.6">
         <artifact name="sslcontext-kickstart-bom-8.3.6.pom">
            <sha256 value="bd1c39714f8194fb506e986dbe779371644668786ce297543dc2e48fae21240e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.github.hakky54" name="sslcontext-kickstart-parent" version="8.3.6">
         <artifact name="sslcontext-kickstart-parent-8.3.6.pom">
            <sha256 value="5a1e0c21ee58f40bb31a9a4590ae939c3333fe81f4c030fcc185d7161e092d8a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.github.java-diff-utils" name="java-diff-utils" version="4.12">
         <artifact name="java-diff-utils-4.12.jar">
            <sha256 value="9990a2039778f6b4cc94790141c2868864eacee0620c6c459451121a901cd5b5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="java-diff-utils-4.12.pom">
            <sha256 value="c26e097edc8ec6805d1319817d23d4e496cc1015cc55dc5202112d8f6a9165fc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.github.java-diff-utils" name="java-diff-utils-parent" version="4.12">
         <artifact name="java-diff-utils-parent-4.12.pom">
            <sha256 value="d811cf9f118cc2cad130c9427ad55c174d4c0a6f1a00ac1ae1c9bcbec5c44b19" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-all" version="1.70.0">
         <artifact name="grpc-all-1.70.0.jar">
            <sha256 value="128cba4d244df0caaae9d51653065cdfbf627a793ace044af5e47814484c896d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-all-1.70.0.pom">
            <sha256 value="2a95cab7e62eaf53c2a7f2e96fe096f750bff32fd3d6819f5e0f37212eb20d01" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-alts" version="1.70.0">
         <artifact name="grpc-alts-1.70.0.jar">
            <sha256 value="b4b2125e8b3bbc2b77ed7157f289e78708e035652b953af6bf90d7f4ef98e1b5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-alts-1.70.0.pom">
            <sha256 value="264fec0a1008e3e8dab9126bcb450f10a4c6e1213e8ab9f3395059715c3f1221" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-api" version="1.70.0">
         <artifact name="grpc-api-1.70.0.jar">
            <sha256 value="45faf2ac1bf2791e8fdabce53684a86b62c99b84cba26fb13a5ba3f4abf80d6c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-api-1.70.0.pom">
            <sha256 value="0bbf92ad00be7cd4263170dab43002d72f4001dcdb9612fb6f4a1f55e24d04cb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-auth" version="1.70.0">
         <artifact name="grpc-auth-1.70.0.jar">
            <sha256 value="74d731ee9ad24b0a27301c91b7e29386394b4cfc6b6e968210763c90f11742b2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-auth-1.70.0.pom">
            <sha256 value="460c1a49ccca7b427cfed3c81bb866927fbc0c4e7d30c6db63d557e4469bff25" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-bom" version="1.70.0">
         <artifact name="grpc-bom-1.70.0.pom">
            <sha256 value="d3925e69f79ff3f095ec77a956c3744293e5c60f75c837159f12e764043627b5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-context" version="1.70.0">
         <artifact name="grpc-context-1.70.0.jar">
            <sha256 value="eb2824831c0ac03e741efda86b141aa863a481ebc4aaf5a5c1f13a481dbb40ff" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-context-1.70.0.pom">
            <sha256 value="ae761bb574d49a40f9f7723c08ad0eb423930df408577170774db5a2e55fbf14" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-core" version="1.70.0">
         <artifact name="grpc-core-1.70.0.jar">
            <sha256 value="c2b5576b8b363b1b1006673c492d912500baaa1581430a7f9c05e82cc5bdfba4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-core-1.70.0.pom">
            <sha256 value="e3f0345eea6a69294fc06b8ed42cb02c1bf72b99dc65ca70bc2193c65b665f4a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-gcp-csm-observability" version="1.70.0">
         <artifact name="grpc-gcp-csm-observability-1.70.0.jar">
            <sha256 value="bf8f4836fb5d12de6f53ddef29dd6fdca487c4bb3ff5d0e55a2787b7bd9b7b36" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-gcp-csm-observability-1.70.0.pom">
            <sha256 value="771a5f6059099dc48a254f1e94ba1cb1eb1458e35b46da6f946768705d2b8dfb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-grpclb" version="1.70.0">
         <artifact name="grpc-grpclb-1.70.0.jar">
            <sha256 value="887c5592bff6c4f39aeffcda70c8d1bb87058cd884d1cd38c70963e2b7850957" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-grpclb-1.70.0.pom">
            <sha256 value="b7c5483a6ab2c3bbf56ffede9f098eca1f11e119375584357ed5d1074991247e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-inprocess" version="1.70.0">
         <artifact name="grpc-inprocess-1.70.0.jar">
            <sha256 value="d9410b06d39383980e1489785d9b347c868839764fb69e588327471d5b73e79f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-inprocess-1.70.0.pom">
            <sha256 value="d717230dd6446bd86b45b97014608e1e73ed4d2fe2b8e273f6dc74f9227f887c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-netty" version="1.70.0">
         <artifact name="grpc-netty-1.70.0.jar">
            <sha256 value="466bc29f36bb3b33ba6fa044d17cdfac494e5715ff606e1794541d0b90b42c16" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-netty-1.70.0.pom">
            <sha256 value="4c692d17b80c2ad0fe9c65d400754bd34cdcea0662256bb88ff0528aa6603dac" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-netty-shaded" version="1.70.0">
         <artifact name="grpc-netty-shaded-1.70.0.jar">
            <sha256 value="e5c53df09a13f2474d37e0ff07b6c74f7cc961879a352f4bc92c9463bcc14164" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-netty-shaded-1.70.0.pom">
            <sha256 value="f40ea86075298b6ea1305bace807be9cfb721c5d1ef7e8c74b099a42482570b4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-okhttp" version="1.70.0">
         <artifact name="grpc-okhttp-1.70.0.jar">
            <sha256 value="f6266c2996585ebd0bd7eb6ee98e6f09b96075c3b7d03477fcb27306fb539fa2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-okhttp-1.70.0.pom">
            <sha256 value="05b3863d7dcec2586ccf790b64ab7c8dc80e6d4412160b8dbf07a71640810034" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-opentelemetry" version="1.70.0">
         <artifact name="grpc-opentelemetry-1.70.0.jar">
            <sha256 value="7e8bf922182dc258b15987c360be2de1657d21132c22b76e8a8dbaffd659345a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-opentelemetry-1.70.0.pom">
            <sha256 value="cffe37e7e4ba497d5337caffaf108850131eef25c4d7fbc994964a2022c62a95" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-protobuf" version="1.70.0">
         <artifact name="grpc-protobuf-1.70.0.jar">
            <sha256 value="9b98039ed826604c46d6ac8f8a182d413d348ec6abe26467736b05aa92e7e1d3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-protobuf-1.70.0.pom">
            <sha256 value="bdb82f11f24cd2a466f19cea98e3805ce5cd13f4dfac40bb30264a686378fb68" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-protobuf-lite" version="1.70.0">
         <artifact name="grpc-protobuf-lite-1.70.0.jar">
            <sha256 value="e7cc2ca8981672851cbebf83a24bfb93c1b2b058e75c1a817a757b914f33403d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-protobuf-lite-1.70.0.pom">
            <sha256 value="9f7b425c8f30296d84a81f42b6c46ff982ff1bc0e95291476ada48610ba02a38" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-rls" version="1.70.0">
         <artifact name="grpc-rls-1.70.0.jar">
            <sha256 value="3f4d0d4bb59848b66422de34b6ce6a452f6526c7905b5d16ce437294c761bd6b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-rls-1.70.0.pom">
            <sha256 value="23eb979c3e407580fa1ff51a7b97a9c97366313326b68df865ba976916980595" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-services" version="1.70.0">
         <artifact name="grpc-services-1.70.0.jar">
            <sha256 value="16207a71c2de10960fc0773136d6990609423a34ddf1babba4cf959196c96b74" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-services-1.70.0.pom">
            <sha256 value="513901c016650152d62944f31a9690fed4332f26b781d789a42dbc7cfba700f3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-servlet" version="1.70.0">
         <artifact name="grpc-servlet-1.70.0.jar">
            <sha256 value="610df82c3e576d2b23623b5c056621302f2c83017a2d4fa4aa4fbe355cec0c13" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-servlet-1.70.0.pom">
            <sha256 value="8a782462166df6646b057801a35eee1562d690471ab35084a10ad30e175470b3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-servlet-jakarta" version="1.70.0">
         <artifact name="grpc-servlet-jakarta-1.70.0.jar">
            <sha256 value="3bdd343143e518dd2bed25bac4bd73445fb079789ce1562c05e28338c6721665" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-servlet-jakarta-1.70.0.pom">
            <sha256 value="3691cd3280ec94a3f8c167bd1ee0c7ed5b94affef6e4f0810220739062686b6b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-stub" version="1.70.0">
         <artifact name="grpc-stub-1.70.0.jar">
            <sha256 value="5adaa1ec1f744b67ae14a8dbc39c9589c010fad0fd557b0a02966202e4d23a18" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-stub-1.70.0.pom">
            <sha256 value="91a91b7d0e802674a453a8079b609542ffb52230ec6bfd376d1758eec5f63ad2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-testing" version="1.70.0">
         <artifact name="grpc-testing-1.70.0.jar">
            <sha256 value="4f910c09d81a863d7613cdb80d870477f396a6634a3cae2ce3bd21597cf29422" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-testing-1.70.0.pom">
            <sha256 value="4a59991ee352b060b55098f4ed9c5c822fb6637f958bde02e3ce67d1ea0950f1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-util" version="1.70.0">
         <artifact name="grpc-util-1.70.0.jar">
            <sha256 value="683aff93d2cabc44ff21dc9ab7794f8ae7b4c65d18748c8474535311eabe8dc4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-util-1.70.0.pom">
            <sha256 value="50160173c94975957041fa5fe70736ef2946d70f61de7f68f817fd01d35ae718" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.grpc" name="grpc-xds" version="1.70.0">
         <artifact name="grpc-xds-1.70.0.jar">
            <sha256 value="96faa7cf98a41e2e916a3eafad65b18089d5a0a7242d772f8d461e4a43738074" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="grpc-xds-1.70.0.pom">
            <sha256 value="3b2b9f4bdb4a4993f5509de57ae29b220dfc8c7b3a01df7e019835843fef2248" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-all" version="4.1.115.Final">
         <artifact name="netty-all-4.1.115.Final.pom">
            <sha256 value="63bc9f25de4e53d6b7fee665fa5e4c73236a4015af3d388db99038b84f79fef5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-all" version="4.1.118.Final">
         <artifact name="netty-all-4.1.118.Final.jar">
            <sha256 value="44790974f057722b9d9d19dcb7afb91504eeb46d81fa2494db4f0fe01c5b7242" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-all-4.1.118.Final.pom">
            <sha256 value="634075f04ebe719a681fc7a653e43900b0ff20104d6705d5852c1a6ec8b2cc83" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-bom" version="4.1.107.Final">
         <artifact name="netty-bom-4.1.107.Final.pom">
            <sha256 value="c36997558bfb4e11f200dda6ee2e41a45f2df9e1aef678eda08e79dd8938c210" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-bom" version="4.1.115.Final">
         <artifact name="netty-bom-4.1.115.Final.pom">
            <sha256 value="25dc8bb8337dfc186c49fc8cf4f6a5b6c7cf4146362f5f440dacadcd0df9761b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-bom" version="4.1.118.Final">
         <artifact name="netty-bom-4.1.118.Final.pom">
            <sha256 value="900f78fd6eb8c6a67b4c9a39d9e5cd96f9eca270b12d7090bc5057efe7e8286a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-buffer" version="4.1.112.Final">
         <artifact name="netty-buffer-4.1.112.Final.pom">
            <sha256 value="b8607b9ee28dc0dc2a7ead92d8dc397c1685ca230976cc99ac1d28d78e078bd6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-buffer" version="4.1.115.Final">
         <artifact name="netty-buffer-4.1.115.Final.jar">
            <sha256 value="4a7b331d3770c566ab70eb02a0d1feed63b95cf6e4d68c8fe778c4c9de2d116d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-buffer-4.1.115.Final.pom">
            <sha256 value="94a3581ea9e4ffc385bb237960f68ef1e84089f164eae8629e8fd66d8aaf0139" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-buffer" version="4.1.118.Final">
         <artifact name="netty-buffer-4.1.118.Final.jar">
            <sha256 value="0eea4e8666a9636a28722661d8ba5fa8564477e75fec6dd2ff3e324e361f8b3c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-buffer-4.1.118.Final.pom">
            <sha256 value="95cbe6e3e341ee49bcffd3257cf78fa5785e465b77a734bcfc2f93509d43fe64" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec" version="4.1.112.Final">
         <artifact name="netty-codec-4.1.112.Final.pom">
            <sha256 value="910f8be0d0e0e193e4637c5e6fa248dfbe10f82e57cc7fb371566ea73f9b7019" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec" version="4.1.115.Final">
         <artifact name="netty-codec-4.1.115.Final.jar">
            <sha256 value="cd189afb70ec6eacfcdfdd3a5f472b4e705a5c91d5bd3ef0386421f2ae15ec77" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-4.1.115.Final.pom">
            <sha256 value="ac166597e81179ca8300276605408910cc030efec12236ce9c38fefc16801aa0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec" version="4.1.118.Final">
         <artifact name="netty-codec-4.1.118.Final.jar">
            <sha256 value="4abd215fd1ed7ce86509d169cc9cbede5042176c265a79b3b70602b017226c3f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-4.1.118.Final.pom">
            <sha256 value="0f1d81390dca4588d87d3fe15d8c52f26bb2ea6d80c90ab93a97e64dfd7a1cdd" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-dns" version="4.1.115.Final">
         <artifact name="netty-codec-dns-4.1.115.Final.jar">
            <sha256 value="23dd6806bcc326855f13e69838c6411d0490e6b1aeb12e217a19a3dd6ad3f10d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-dns-4.1.115.Final.pom">
            <sha256 value="73976d702597a4d7597f82634fd17d1161a486972c7306b7e7cccb582533ea4e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-dns" version="4.1.118.Final">
         <artifact name="netty-codec-dns-4.1.118.Final.jar">
            <sha256 value="e115e42ca1e3cc8d85e3a632d8faa102d18c0ebc1fa4511af30bec79f8c147d4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-dns-4.1.118.Final.pom">
            <sha256 value="dc1e18117cf0d84983aef3774b15bf8eff9a56fae98ce98a4cedb69cc07e471a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-haproxy" version="4.1.118.Final">
         <artifact name="netty-codec-haproxy-4.1.118.Final.jar">
            <sha256 value="442adc9857f129389cb7fc43c8d602dfb60305c75f14338d60e9f24646b307fe" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-haproxy-4.1.118.Final.pom">
            <sha256 value="53f8a7ffd904cc9b55100535c3ecf729b59f42725b70d2644ae66ced218cfa08" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-http" version="4.1.112.Final">
         <artifact name="netty-codec-http-4.1.112.Final.pom">
            <sha256 value="30b7911f152fc3df54eaedbc3e048a670d5f793f81c458ac18b1511309f66e20" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-http" version="4.1.115.Final">
         <artifact name="netty-codec-http-4.1.115.Final.jar">
            <sha256 value="e6dbe971c59373bbae9802021c63b9bc1d8800fead382863d67e79e79b023166" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-http-4.1.115.Final.pom">
            <sha256 value="fa7064f72ee52e136c9b6bfb5a1a0d510dcb73092d71941a39514c322a59de40" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-http" version="4.1.118.Final">
         <artifact name="netty-codec-http-4.1.118.Final.jar">
            <sha256 value="09822d785e9a794838031ddd5346cf419b30c036a981c2e277a062bea884174b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-http-4.1.118.Final.pom">
            <sha256 value="efa0a65fa1bccbd98dd14def99fda66dae999f6efc4b6418803bb50c6713c716" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-http2" version="4.1.112.Final">
         <artifact name="netty-codec-http2-4.1.112.Final.pom">
            <sha256 value="663b3990dcb8c3329521c3647716970cb37d9cd2d2be2037cac887c8be5d1119" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-http2" version="4.1.115.Final">
         <artifact name="netty-codec-http2-4.1.115.Final.jar">
            <sha256 value="cbed9829a5d582e91e314e209edce9a0c2eb369f23bb4fb74a5bc8b7990222c2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-http2-4.1.115.Final.pom">
            <sha256 value="88747b3e38e21cc1f42eaa44d301423482c0fc26c467b3f78fb9edfbde93a3e1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-http2" version="4.1.118.Final">
         <artifact name="netty-codec-http2-4.1.118.Final.jar">
            <sha256 value="68da0b1a34dceb00a6f9f6f788fb2f6b7b9e4adba8c70658ac2bd7eb898b97ae" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-http2-4.1.118.Final.pom">
            <sha256 value="4224460f69f6f67982c624a0d503c786a2450111b31ab15c075e0b28c94e11fe" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-memcache" version="4.1.118.Final">
         <artifact name="netty-codec-memcache-4.1.118.Final.jar">
            <sha256 value="e808b9fb1e0c165517782fa7efa97b25b69eb29029f8c39cd934c4a740aaf4c6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-memcache-4.1.118.Final.pom">
            <sha256 value="e5842c75785fe96c7290783ea72593c3d0775362adb0dbefe6acc06e4fbaac52" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-mqtt" version="4.1.118.Final">
         <artifact name="netty-codec-mqtt-4.1.118.Final.jar">
            <sha256 value="11055eaff1fca2a4df3f97b8a5a86732366ca99ff823ceef1c1a4bc4ddcb3b20" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-mqtt-4.1.118.Final.pom">
            <sha256 value="6daa144a9c589a058249c7f2f035b3f72710e6cb2357ef49ed5bcf86789be71e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-redis" version="4.1.118.Final">
         <artifact name="netty-codec-redis-4.1.118.Final.jar">
            <sha256 value="1a516f6c038976cfd8132d10ff01075d184eb94b168f95db36b7aef283fc2337" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-redis-4.1.118.Final.pom">
            <sha256 value="991cd92af74549a4292c34583826717a41c30b10575e90d8cb0c52ca680f77aa" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-smtp" version="4.1.118.Final">
         <artifact name="netty-codec-smtp-4.1.118.Final.jar">
            <sha256 value="ea97d0622440bcdf20278252d8c276430e17749204e169cd3ee60199e6d3ba3c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-smtp-4.1.118.Final.pom">
            <sha256 value="6d015f95098fc3478b02350257f4944b880d066cc248be817e630f8bf28ef3ee" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-socks" version="4.1.115.Final">
         <artifact name="netty-codec-socks-4.1.115.Final.jar">
            <sha256 value="e9b1cc744dc6195894450b1fd4d271a821ab167fe21ae3c459b27cdadc70e81f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-socks-4.1.115.Final.pom">
            <sha256 value="08b3c1acc77abdbadeef08c8cdf080e1bcceffe5f84751f60d89fc0bcbaaa2fc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-socks" version="4.1.118.Final">
         <artifact name="netty-codec-socks-4.1.118.Final.jar">
            <sha256 value="094465e3cfb3aef0fca38ed82b801f53a6c8be7ae1f83ab0c1b2e8ece2586840" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-socks-4.1.118.Final.pom">
            <sha256 value="9382fa2a4497e4de147b3aa8c193f5b7cabef56671cce8e12bc593cc7a84d293" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-stomp" version="4.1.118.Final">
         <artifact name="netty-codec-stomp-4.1.118.Final.jar">
            <sha256 value="a9a6c2ed43cc1f0769f9e130f9df5c76be9fd9294e4611227341d8b28689ec57" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-stomp-4.1.118.Final.pom">
            <sha256 value="32ca71a69d0ffe32f26f4c2f5a29a1d54b4821548a8135e84588f7ce7051b75e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-codec-xml" version="4.1.118.Final">
         <artifact name="netty-codec-xml-4.1.118.Final.jar">
            <sha256 value="47ac0297e67213a4dd24ef4e81f170a87787df92ac6f2a2a9da051f272a5a482" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-codec-xml-4.1.118.Final.pom">
            <sha256 value="2461f368ad805297376f6ffbaa06a0335f6babb387494f684664d82e6b029c45" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-common" version="4.1.112.Final">
         <artifact name="netty-common-4.1.112.Final.pom">
            <sha256 value="d29a43df1ada90dd45121d753475f2672b94261322a919907dd428db74e214be" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-common" version="4.1.115.Final">
         <artifact name="netty-common-4.1.115.Final.jar">
            <sha256 value="39f1b5a2aaa4eab5d036dfd0486e35a4276df412e092d36b2d88b494705a134d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-common-4.1.115.Final.pom">
            <sha256 value="6286361f5dd6a7b26c42909242bb8629e3aeec255ad0d3ff131bcb5152c31b21" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-common" version="4.1.118.Final">
         <artifact name="netty-common-4.1.118.Final.jar">
            <sha256 value="65cce901ecf0f9d6591cc7750772614ab401a84415dc9aec9da4d046f0f9a77c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-common-4.1.118.Final.pom">
            <sha256 value="34290415e3f7961b196f7637c82a617d1038112291b31cbbc47200d1da326e82" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-handler" version="4.1.112.Final">
         <artifact name="netty-handler-4.1.112.Final.pom">
            <sha256 value="bf3eaee25f851038a3160d01da70b21725c8b9ecbb7666328c66209fbcdc213c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-handler" version="4.1.115.Final">
         <artifact name="netty-handler-4.1.115.Final.jar">
            <sha256 value="5972028cc863b74927ce0d11fb8d58f65da2560bef5602fe8ce8903bd306ca07" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-handler-4.1.115.Final.pom">
            <sha256 value="8c28025a352fc03846ce04960ab9843418bc6c82cbe5459e8060a4f3459efe90" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-handler" version="4.1.118.Final">
         <artifact name="netty-handler-4.1.118.Final.jar">
            <sha256 value="26e3f8a5e859fd62cf3c13dc6d75e4e18879f000a5d0ad7f58f8679675d23dae" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-handler-4.1.118.Final.pom">
            <sha256 value="64b4b591ac4df4e9683a3fa17f73defb761af072c56dc2de4033e180c56b1049" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-handler-proxy" version="4.1.115.Final">
         <artifact name="netty-handler-proxy-4.1.115.Final.jar">
            <sha256 value="807e67cfb17136927d11db42df62031169d1fa0883e13f254906994c84ffbe87" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-handler-proxy-4.1.115.Final.pom">
            <sha256 value="f9936b2da7adcecef5c4ffad772067b7de5d0e359b83e6fd39b4a49d11706a10" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-handler-proxy" version="4.1.118.Final">
         <artifact name="netty-handler-proxy-4.1.118.Final.jar">
            <sha256 value="fef926126f44c668968dd3e2389c2552981d452e6dfc23b1f9bd03db92c21f96" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-handler-proxy-4.1.118.Final.pom">
            <sha256 value="65c5c63a7bd6449a6d71e03c609d81a7a671e339abee54418477a3b388a19ef5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-handler-ssl-ocsp" version="4.1.118.Final">
         <artifact name="netty-handler-ssl-ocsp-4.1.118.Final.jar">
            <sha256 value="c311f4f3389432544bf103162b40736d9b67001dbae1cb92d67adcb56f50f1c6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-handler-ssl-ocsp-4.1.118.Final.pom">
            <sha256 value="d6a1ea624d03d5817a38adc238cd2a6cc13ffae3a40b3d1f0a91d0ce6bc2a7a7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-parent" version="4.1.112.Final">
         <artifact name="netty-parent-4.1.112.Final.pom">
            <sha256 value="588917836a71db4fa8cbbdf73a7206f9eac21f3437fe9bbc71c81d8fcff5b176" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-parent" version="4.1.115.Final">
         <artifact name="netty-parent-4.1.115.Final.pom">
            <sha256 value="d832942b8e2a71c2ccfdd0247a8b840105ce40aaeec4b8de36358f28d93941e3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-parent" version="4.1.118.Final">
         <artifact name="netty-parent-4.1.118.Final.pom">
            <sha256 value="d966daebad7c721dfbf8fecd94f07b126f6fcb2d1f85b9c87999fd999ebc68c8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-resolver" version="4.1.112.Final">
         <artifact name="netty-resolver-4.1.112.Final.pom">
            <sha256 value="f91397677f2a781cd104f249b6e4d73c005d9236655f66775faaa99916d1b155" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-resolver" version="4.1.115.Final">
         <artifact name="netty-resolver-4.1.115.Final.jar">
            <sha256 value="7b3455d14f59828765a00573bc3967dc59379e874bd62a67eb1926d6512109d1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-resolver-4.1.115.Final.pom">
            <sha256 value="53a491cfc876083ebd67e9c315c18c2348bb28f5366ca5d178bd8e8611f0ab4e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-resolver" version="4.1.118.Final">
         <artifact name="netty-resolver-4.1.118.Final.jar">
            <sha256 value="3170c225972c18b6850d28add60db15bb28d83c4e3d5b686ca220e0bd7273c8a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-resolver-4.1.118.Final.pom">
            <sha256 value="872dcbd88229c2e9180a3caa75584c42ced88a516c49a68dd896372164b2a94f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-resolver-dns" version="4.1.115.Final">
         <artifact name="netty-resolver-dns-4.1.115.Final.jar">
            <sha256 value="4aca31593e5896c64ab7e041bbc6c0d851bd9634ec3a4354208141a35576619f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-resolver-dns-4.1.115.Final.pom">
            <sha256 value="8d1e3143825e0244e1dd614b2340deb00f4ee07ef615faa855bd195100776789" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-resolver-dns" version="4.1.118.Final">
         <artifact name="netty-resolver-dns-4.1.118.Final.jar">
            <sha256 value="c0e0fdaffaba849e3145b2b96288fc8fc6f3b2a623cf72aaba708288348e4938" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-resolver-dns-4.1.118.Final.pom">
            <sha256 value="d6a6c36e94772f70b86aab4cada5f8c56aedf924d0006735da42b35d5ef9c6f7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-resolver-dns-classes-macos" version="4.1.118.Final">
         <artifact name="netty-resolver-dns-classes-macos-4.1.118.Final.jar">
            <sha256 value="fb51c17c4eaba431da57dcf185d9a02c767dbc2925f281e19a4f14ba1b0ae5ed" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-resolver-dns-classes-macos-4.1.118.Final.pom">
            <sha256 value="f88c40964c5732808d3ceff427cb9ced7a2f2d77f260c353df3d97f2f0d70371" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-resolver-dns-native-macos" version="4.1.118.Final">
         <artifact name="netty-resolver-dns-native-macos-4.1.118.Final-osx-aarch_64.jar">
            <sha256 value="e1e95fe184946a0e14171c494e54456b9160f9f9f2a07e5db30426ed9f9b76d8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-resolver-dns-native-macos-4.1.118.Final-osx-x86_64.jar">
            <sha256 value="0bb6cd68b24b844eae224a1846909adbdffa7f78407bb588bf77f01f334b5bb5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-resolver-dns-native-macos-4.1.118.Final.pom">
            <sha256 value="62bd872c2982af7de2438ebdbd7eff1e5ee6aa475e4c683d52990eebfc05bad4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-tcnative-boringssl-static" version="2.0.70.Final">
         <artifact name="netty-tcnative-boringssl-static-2.0.70.Final.jar">
            <sha256 value="3f7b4c3a51737965cd5b53777782c125784420458d96513cfac7412e4d1fa0c3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-tcnative-boringssl-static-2.0.70.Final.pom">
            <sha256 value="df443ea183f355f099a796a7ee067a566c7e3e94e71dd9c9691f9bbafc2b3535" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-tcnative-classes" version="2.0.70.Final">
         <artifact name="netty-tcnative-classes-2.0.70.Final.jar">
            <sha256 value="a79c1579313d4ad48a3ecc1d01a25da06d22d6449c3bcc369c2318749bcf55bc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-tcnative-classes-2.0.70.Final.pom">
            <sha256 value="411b46884bf6d25eba1f64535391ec5b1087e918e0e53d224e4220a04fb32675" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-tcnative-parent" version="2.0.70.Final">
         <artifact name="netty-tcnative-parent-2.0.70.Final.pom">
            <sha256 value="8075a5e549677011a07175d9b8da963a58b884b32e8e101b75e78152173fdc35" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport" version="4.1.112.Final">
         <artifact name="netty-transport-4.1.112.Final.pom">
            <sha256 value="93ea740001420a754079207dcdc79628c77645611df161443db90e5ce7654200" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport" version="4.1.115.Final">
         <artifact name="netty-transport-4.1.115.Final.jar">
            <sha256 value="c3d71faaa736ffd2c9260ab0b498024b814c39c7d764bea8113fa98de6e2bdd2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-4.1.115.Final.pom">
            <sha256 value="9677471c5409adced1f6cdb65a3ad8015e2e970990aba497a4eeca18abc363f9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport" version="4.1.118.Final">
         <artifact name="netty-transport-4.1.118.Final.jar">
            <sha256 value="ab3751e717daef9c8d91e4d74728a48730bd8530b72e2466b222b2ea3fb07db9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-4.1.118.Final.pom">
            <sha256 value="621f4c5672204a7b4372397b0bf8d7e446cd9f660affc0d0339c059c500a7032" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport-classes-epoll" version="4.1.112.Final">
         <artifact name="netty-transport-classes-epoll-4.1.112.Final.jar">
            <sha256 value="96cf2e6622ea70e2bc67aed373607df32863f863d0d7b8c9c94d468efd1d0638" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-classes-epoll-4.1.112.Final.pom">
            <sha256 value="8ec13cdd432f6ba48b2df879058c2bc7030c70036bd5e9a22b48295c8d3a7f4f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport-classes-epoll" version="4.1.118.Final">
         <artifact name="netty-transport-classes-epoll-4.1.118.Final.jar">
            <sha256 value="bd86e6d41e1f6053f9577931655236259778ab045646e1e6ab04150f070864f3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-classes-epoll-4.1.118.Final.pom">
            <sha256 value="f962b4579a4be0cd4bba23ea89ae3b84e0c9b8d16486e4c1d311346eb6fc731f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport-classes-kqueue" version="4.1.118.Final">
         <artifact name="netty-transport-classes-kqueue-4.1.118.Final.jar">
            <sha256 value="9ac68d26efb78d74a24eeec8309d60bc80265655b60c44920dcac0828db4e8e2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-classes-kqueue-4.1.118.Final.pom">
            <sha256 value="28c71e4396343f25438d619d2953f066d0cf0f5971aea957fab1e318a0135290" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport-native-epoll" version="4.1.118.Final">
         <artifact name="netty-transport-native-epoll-4.1.118.Final-linux-aarch_64.jar">
            <sha256 value="53b3bec1d019bd3db6eeed30c6a770b49052ddd8fa1566796ae759e62b572425" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-native-epoll-4.1.118.Final-linux-riscv64.jar">
            <sha256 value="e7b7a5403a580be2f38c99c2559e551ebb563fdddd89d7f6e142fb1b475ba95d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-native-epoll-4.1.118.Final-linux-x86_64.jar">
            <sha256 value="c95f66b9ab3f7fb6e666a81a5d0120e0c6acddf8fdf440e2ba212bfaf76a7c73" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-native-epoll-4.1.118.Final.jar">
            <sha256 value="892c85d067ecf495808b7090d8b62afa0248404b4e07cde88287ea116dfa6fba" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-native-epoll-4.1.118.Final.pom">
            <sha256 value="cab7bfb6c06d1f88199dc10f8681d92def080d116b7669fc596a750a37f40e50" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport-native-kqueue" version="4.1.118.Final">
         <artifact name="netty-transport-native-kqueue-4.1.118.Final-osx-aarch_64.jar">
            <sha256 value="599f32b702fb515f6fe15064250f525ad7eee13676dd3bcfc4c42704a17c7c7e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-native-kqueue-4.1.118.Final-osx-x86_64.jar">
            <sha256 value="dc0a100920afadd39c252ba682ea62bfdb139945eaa9efdb6472f20b6bae4ef2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-native-kqueue-4.1.118.Final.jar">
            <sha256 value="d7684931403b08e20a7926c171e294deccadcebdc500a90e68d04f978cddae41" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-native-kqueue-4.1.118.Final.pom">
            <sha256 value="9d9bb0f52879c53907b0a60898f6e71e7997169cbfa84307154b503c12209146" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport-native-unix-common" version="4.1.112.Final">
         <artifact name="netty-transport-native-unix-common-4.1.112.Final.pom">
            <sha256 value="ba7456ccde4ad207cc59341bd1bd2a0938231daa9aebb4f84f20b18becd969ef" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport-native-unix-common" version="4.1.115.Final">
         <artifact name="netty-transport-native-unix-common-4.1.115.Final.jar">
            <sha256 value="4b03e716272657c296b0204b57c140b2b2ca96b1a746c92da41f595892ec6d88" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-native-unix-common-4.1.115.Final.pom">
            <sha256 value="96075271c578faadffec6f133991ea30884a69a41e07245a5ff8d5e7e5dd9f07" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport-native-unix-common" version="4.1.118.Final">
         <artifact name="netty-transport-native-unix-common-4.1.118.Final.jar">
            <sha256 value="69b16793d7b41ea76a762bd2bd144fc4f7c39c156a7a59ebf69baeb560fb10b7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-native-unix-common-4.1.118.Final.pom">
            <sha256 value="c37dfa448fda576e35d2a788a346a9a4518198acc53759922584abb86d21dfdb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport-rxtx" version="4.1.118.Final">
         <artifact name="netty-transport-rxtx-4.1.118.Final.jar">
            <sha256 value="e5cdde9a3b70ea9cca796872c86598bff60883332151794b3900d533e1316739" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-rxtx-4.1.118.Final.pom">
            <sha256 value="054eac7bbcb2cf239b39662b0dbd62766882d81edcee946c802b57db37ab0b4b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport-sctp" version="4.1.118.Final">
         <artifact name="netty-transport-sctp-4.1.118.Final.jar">
            <sha256 value="1f9f992bb1681637932dbd0449dc931088ea8bb25f968c07524ef0f193ea6c52" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-sctp-4.1.118.Final.pom">
            <sha256 value="831a411a7aca8d3b75b1dcca93196fe718cdd12ea79b4c84d139b2739624ebb9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.netty" name="netty-transport-udt" version="4.1.118.Final">
         <artifact name="netty-transport-udt-4.1.118.Final.jar">
            <sha256 value="f8ca98ebc019f4fed190c959abfd8d32996250c1a4f97f4fd88c99ab97759d1b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-transport-udt-4.1.118.Final.pom">
            <sha256 value="c6a6f04a27746cb1c6b5b6de29529b569418c928e9e2ba1b24a5314a6ab8d10c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opencensus" name="opencensus-api" version="0.31.1">
         <artifact name="opencensus-api-0.31.1.jar">
            <sha256 value="f1474d47f4b6b001558ad27b952e35eda5cc7146788877fc52938c6eba24b382" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opencensus-api-0.31.1.pom">
            <sha256 value="556f427e12090efb36a6087f7410abfe45de10107292d96fa63e4175174ecb76" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opencensus" name="opencensus-contrib-http-util" version="0.31.1">
         <artifact name="opencensus-contrib-http-util-0.31.1.jar">
            <sha256 value="3ea995b55a4068be22989b70cc29a4d788c2d328d1d50613a7a9afd13fdd2d0a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opencensus-contrib-http-util-0.31.1.pom">
            <sha256 value="ebe22c422217d662c7cee99476f0b52c80577ed445786ac299251befaa4c075b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-api" version="1.47.0">
         <artifact name="opentelemetry-api-1.47.0.jar">
            <sha256 value="6566f1f1133d611ff4e8b8fdb8eb18577b970425620315363ee9be43843b14bf" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-api-1.47.0.module">
            <sha256 value="cc213a09a23edbf6d174ebbb93fb65d2809e0fca3f9b6a3455b8bb6bdc44fe34" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-api-1.47.0.pom">
            <sha256 value="5f6c6c2cdce236151f040d4a9a9f8ec105b9195d0f16b5aed85572f300eac91d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-api-incubator" version="1.47.0-alpha">
         <artifact name="opentelemetry-api-incubator-1.47.0-alpha.jar">
            <sha256 value="49e40abd6fe379561a0842e39b6ebf7c239748d2ef72b96d1ad91054df315e46" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-api-incubator-1.47.0-alpha.module">
            <sha256 value="7ffa8c2a50dd03f1d95d81fd5c0b191b0ec4782aa411147ae0d0513da29628e6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-bom" version="1.44.1">
         <artifact name="opentelemetry-bom-1.44.1.module">
            <sha256 value="325613776fb98e86c7c53a13b6d61fcede7f2584f7725ded6d84d1bdf458656d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-bom-1.44.1.pom">
            <sha256 value="185c9efbbce4a7e59667078812a215554ed514b17dc1e319bfbc22bc011e62d7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-bom" version="1.47.0">
         <artifact name="opentelemetry-bom-1.47.0.module">
            <sha256 value="41d2b58a540c9a44358809b2d636480ed47c025a2fa7becd7a78c4cb74e494be" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-bom-alpha" version="1.44.1-alpha">
         <artifact name="opentelemetry-bom-alpha-1.44.1-alpha.module">
            <sha256 value="3bb69ad24a2ac599738138c7e7af71df0076f9ded15a176e78c9d0afdee67098" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-bom-alpha-1.44.1-alpha.pom">
            <sha256 value="3347fb4d7a7ccb56def084d60b6416aead7360bcba57b8729dc3ef7e3db9d442" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-context" version="1.47.0">
         <artifact name="opentelemetry-context-1.47.0.jar">
            <sha256 value="15b4fc4234e6dca6d54800d572694ecbd07ba52c15fc5b221b4da5517ce8d90d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-context-1.47.0.module">
            <sha256 value="338681e8dc1a1f64ffb6f7078c46b6f960fe9463799e2debebe7d58c06fc5db3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-context-1.47.0.pom">
            <sha256 value="59432aa776c3f1eb6606b854e77835fa23e524150f7e0dfa3c6510eb57a442c3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-exporter-common" version="1.47.0">
         <artifact name="opentelemetry-exporter-common-1.47.0.jar">
            <sha256 value="691479853712dbdf4ccbd5ec72434732918723f21d7d75b6b57436cca943a330" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-exporter-common-1.47.0.module">
            <sha256 value="749401bd6d2abd1419afd99cb320b8efb854bcde200fc2b16d85d4533576f2fd" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-exporter-common-1.47.0.pom">
            <sha256 value="daf252edffdd4bfe828f91432f75c2a4ab1a4248111613f3d2a6f6cdbf77ae05" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-exporter-otlp" version="1.47.0">
         <artifact name="opentelemetry-exporter-otlp-1.47.0.jar">
            <sha256 value="0bf93192631d8d069f865707abf456432bb8fca316d5a7fd0ff5d1302414c651" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-exporter-otlp-1.47.0.module">
            <sha256 value="be959a5b281ff51e3a2def88a740b9948fb995330bf10525017611f947b80d91" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-exporter-otlp-1.47.0.pom">
            <sha256 value="74ba955a24f7d11af708a551e32e912f758cb89d7f318525f739477787bf730b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-exporter-otlp-common" version="1.47.0">
         <artifact name="opentelemetry-exporter-otlp-common-1.47.0.jar">
            <sha256 value="aa1799740e75486da0b64b1455d24150a5bff64c71a72482bdf3d8e5274d4bd5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-exporter-otlp-common-1.47.0.module">
            <sha256 value="bb1f0403f82476a3e55d294ba99b46abb264ece046e172313b47323080d9af4b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-exporter-otlp-common-1.47.0.pom">
            <sha256 value="bc51b696aca863e8bf9b0c41005b0c211cfd73c01a67264e9d9f71beb3d6238c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-exporter-sender-okhttp" version="1.47.0">
         <artifact name="opentelemetry-exporter-sender-okhttp-1.47.0.jar">
            <sha256 value="d293e4d41fb5dfb322c56a8ff7080cb88f5b09b6c2ab9b7b0b717e2e3da3237c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-exporter-sender-okhttp-1.47.0.module">
            <sha256 value="3fbeebb94774314e21fe127d1731cef9d012f439940e51af375dc190e6901d97" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-exporter-sender-okhttp-1.47.0.pom">
            <sha256 value="5f43939f931695723cb64ddaf5cb6db0fa7a9d01c024d05c80d2f4d4e2b0b09d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-extension-trace-propagators" version="1.47.0">
         <artifact name="opentelemetry-extension-trace-propagators-1.47.0.jar">
            <sha256 value="b456d1e22e7601a3798dff3d202e21333c5cd6155eb02b73c94e3c8a56cf7481" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-extension-trace-propagators-1.47.0.module">
            <sha256 value="e964e668092c2047646e0c6934744628ba9fd2ba9919d1100d6b8a196e8a33f3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-extension-trace-propagators-1.47.0.pom">
            <sha256 value="76d39465b217e11a7c4c39647dc47be826323961fa3b6061b85bf3b3846f7947" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-sdk" version="1.47.0">
         <artifact name="opentelemetry-sdk-1.47.0.jar">
            <sha256 value="4a09eb2ee484769973e14218a34e6da54f35955aa02b26dc5238b0c2ed6a801d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-1.47.0.module">
            <sha256 value="ab014fbd0e716fb4c12954810e9875d1a078f54c50f927fc57b99bc6b732573d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-1.47.0.pom">
            <sha256 value="a7ee3908c2c761cfa44e334ac66686d63aa70826afca6b4f17a77c437a7f1b55" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-sdk-common" version="1.47.0">
         <artifact name="opentelemetry-sdk-common-1.47.0.jar">
            <sha256 value="7ce55666aca7f2e5697a57bd4133e4508a6dc5041685f2d1ef31bb156f32e3bd" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-common-1.47.0.module">
            <sha256 value="51355005e0e6b3dc75d00003e5689660b974467598d0178c2a420f88460fe116" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-common-1.47.0.pom">
            <sha256 value="a02d0f8854caaedd45b6962242fa0c95c2e2c87427d3dff64ead416d1b648bff" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-sdk-extension-autoconfigure" version="1.47.0">
         <artifact name="opentelemetry-sdk-extension-autoconfigure-1.47.0.jar">
            <sha256 value="46fcc7ab3934ff6ed835513e9ae2c881691e38c03e68edfe60595de95814a895" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-extension-autoconfigure-1.47.0.module">
            <sha256 value="165d76f405c950f8a3d1c5a15b719075b674bd0cee5fcfbff2ae921912767adf" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-extension-autoconfigure-1.47.0.pom">
            <sha256 value="6debd33b69f396fc037afd14f3587df6b93a574e24c93817725d8ddd667d43d9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-sdk-extension-autoconfigure-spi" version="1.47.0">
         <artifact name="opentelemetry-sdk-extension-autoconfigure-spi-1.47.0.jar">
            <sha256 value="94348d4263e2c59c7039630cad379d969b62644ed09b6178fa952988559b396a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-extension-autoconfigure-spi-1.47.0.module">
            <sha256 value="dcdf572aa14fe6de80e16030436a098a73f642416d8625a3de71156270d14a3b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-extension-autoconfigure-spi-1.47.0.pom">
            <sha256 value="a65179724d0ffe8998747707b20ca657daf2cbb4b1ba2e7434f651c228220552" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-sdk-logs" version="1.47.0">
         <artifact name="opentelemetry-sdk-logs-1.47.0.jar">
            <sha256 value="302491984b63eebaf4b58bd3ae19d223a55f79090a3e46b40507b49c3cbe9cc5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-logs-1.47.0.module">
            <sha256 value="52abac50a803ce6782ca50b78cd4a9dcdd9774bb837b1dabfb492b0be5657052" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-logs-1.47.0.pom">
            <sha256 value="ecf8af390b8445e25056196cbc4ca9edcbe15614c4e99357ffec3730ed641503" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-sdk-metrics" version="1.47.0">
         <artifact name="opentelemetry-sdk-metrics-1.47.0.jar">
            <sha256 value="7d1442c5ca916ba2513005205d3b8b9bc5dca4e2a84867d0550602a0dfc0bba5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-metrics-1.47.0.module">
            <sha256 value="0f8c8e59d8a570f10c11e216741b47237235564e1588f7d6f82a59d24e31fe01" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-metrics-1.47.0.pom">
            <sha256 value="35a34195f5b83fc0b5b2ab572d76fbc3cdae5b8b41d49144956de112d50ab3b5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry" name="opentelemetry-sdk-trace" version="1.47.0">
         <artifact name="opentelemetry-sdk-trace-1.47.0.jar">
            <sha256 value="03950efd5fa5a276769a593579d8f602742a5d52f9978569326d2a9f9e162546" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-trace-1.47.0.module">
            <sha256 value="d31451ce85987a6680ba48bd75013c662ddbcce6538247e2bf8c8ef834546eb9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-sdk-trace-1.47.0.pom">
            <sha256 value="3164587d8a958d970205733ee54b5448f237062646f761089ba301da890bf1c6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry.contrib" name="opentelemetry-gcp-resources" version="1.40.0-alpha">
         <artifact name="opentelemetry-gcp-resources-1.40.0-alpha.jar">
            <sha256 value="899ccc4306c6c25752ce43fd7ebc9a44aab8e55dfe50366fb3f49437ab996a12" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-gcp-resources-1.40.0-alpha.module">
            <sha256 value="dab2716efa55b527014c34195b224fe0f8e9b7b01b43d089a0d3de6184d0d0d6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry.instrumentation" name="opentelemetry-instrumentation-api" version="2.13.1">
         <artifact name="opentelemetry-instrumentation-api-2.13.1.jar">
            <sha256 value="74a1bdd93cfbd335eeebf5d855b3ec86d1d3f5c65e11cecd984cce22ebd0f45f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-instrumentation-api-2.13.1.module">
            <sha256 value="30b96e79f479f841ff713690fdbee1c91e2369c486ca59cbf91507051bbae4ac" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry.instrumentation" name="opentelemetry-instrumentation-api-incubator" version="2.13.1-alpha">
         <artifact name="opentelemetry-instrumentation-api-incubator-2.13.1-alpha.jar">
            <sha256 value="cf50fccae8d9c333202fa6ab4d338f59c07d9a8bd68ef899401f1d93348132b2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-instrumentation-api-incubator-2.13.1-alpha.module">
            <sha256 value="e06003361cbbb1730af07bf28e5f56faad10ac8eb2cb1956fd2b03898a04af6c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry.instrumentation" name="opentelemetry-instrumentation-bom" version="2.10.0">
         <artifact name="opentelemetry-instrumentation-bom-2.10.0.module">
            <sha256 value="f7881be9e9c8f20cbccff71684ec2cc054bf12adf46dc671bb4e290bbd286b05" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-instrumentation-bom-2.10.0.pom">
            <sha256 value="f785b7118ab4088d01662e73977f29a1495634caab1bb3c687f8fa00d2de3f96" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry.instrumentation" name="opentelemetry-instrumentation-bom-alpha" version="2.10.0-alpha">
         <artifact name="opentelemetry-instrumentation-bom-alpha-2.10.0-alpha.module">
            <sha256 value="f4f66eb95040a8a1ad0a1f55d0fd8d36fea9365ed577c29803402245ccd3c67e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-instrumentation-bom-alpha-2.10.0-alpha.pom">
            <sha256 value="5d7a99d3acddcdaf98abbaa5325324cbcdb8fd773d2e39c76a4319f6f2a6520a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry.instrumentation" name="opentelemetry-okhttp-3.0" version="2.13.1-alpha">
         <artifact name="opentelemetry-okhttp-3.0-2.13.1-alpha.jar">
            <sha256 value="6994f8ec7dba56a04283202b4aa1f5590d2551a5f42677fcf26cc72934d239d3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-okhttp-3.0-2.13.1-alpha.module">
            <sha256 value="74e493e127b6564d672f31d38a1eea99484d1758cf52a74ccb165fbf63706b73" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry.proto" name="opentelemetry-proto" version="1.5.0-alpha">
         <artifact name="opentelemetry-proto-1.5.0-alpha.jar">
            <sha256 value="03bab813f054fd931f9ccc9bf63e199b8ba33c91f661787f99a916a50a26eb32" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-proto-1.5.0-alpha.module">
            <sha256 value="42d149611efae64aeab1a4d1b08ee54f510c9f0f6fce2f93766b4e44a8af6298" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry.semconv" name="opentelemetry-semconv" version="1.30.0">
         <artifact name="opentelemetry-semconv-1.30.0.jar">
            <sha256 value="99c2478a9b803b7d385d1624d5c1ab6f9a64cac5a2dc00f44a350744a1d858ac" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-semconv-1.30.0.module">
            <sha256 value="690f2d4a0d76783949c97da615e988232a536e8903faf568009fcf25baa69dd2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-semconv-1.30.0.pom">
            <sha256 value="67d02b1f0f263adc67cd449b28ed3a3bca071ad3d36fa3aab34e5fb2e3039256" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentelemetry.semconv" name="opentelemetry-semconv-incubating" version="1.30.0-alpha">
         <artifact name="opentelemetry-semconv-incubating-1.30.0-alpha.jar">
            <sha256 value="8ae7c505e8d9a75272c314116f0e94df07c0bb20abb488ad9c203f1166e774ed" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-semconv-incubating-1.30.0-alpha.module">
            <sha256 value="78a9dc063c6ffaca050d2344cb6795f9e89e140b21d5844d7ddfdbac96e77a9c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentelemetry-semconv-incubating-1.30.0-alpha.pom">
            <sha256 value="6b4fb1966dafb4ee9446b20fe2829513c47330fe01cc62856588b29a1a4ea153" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentracing" name="opentracing-api" version="0.33.0">
         <artifact name="opentracing-api-0.33.0.jar">
            <sha256 value="4534541b8e9f41a17bcdf1d09affe45b98c13574db6e529a93a58264b9472c7c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentracing-api-0.33.0.pom">
            <sha256 value="3783e2333ef4ec6c2442204ced5897232eab1185bfe59633629afbf2aa551931" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentracing" name="opentracing-noop" version="0.33.0">
         <artifact name="opentracing-noop-0.33.0.jar">
            <sha256 value="8529f91e10047b2b94cb21b50086a3d3913fa4da43594eddbd9ecf5917efe040" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentracing-noop-0.33.0.pom">
            <sha256 value="1e76b824f918e858cf2e1f81a89fe8c1287a9df463467c789254aeeced8e8295" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentracing" name="opentracing-util" version="0.33.0">
         <artifact name="opentracing-util-0.33.0.jar">
            <sha256 value="22c5dfbb9b0e2f08f7371bf3d68372c7604c804d3129499b43f37a8877c4379e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentracing-util-0.33.0.pom">
            <sha256 value="34b427ffd1c4a16843f15349e597d17645fb25ee7b465292ec7077adb0c66b31" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentracing" name="parent" version="0.33.0">
         <artifact name="parent-0.33.0.pom">
            <sha256 value="2d08dff405159ef344e195b027977f4369e0931af33ff1fbd696dd5efaba6bb7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentracing.contrib" name="opentracing-concurrent" version="0.4.0">
         <artifact name="opentracing-concurrent-0.4.0.jar">
            <sha256 value="261bfc94feecd0f0e7f1443fa9040409ed9a1f7fbe122f34fee0c11ac295cdc5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentracing-concurrent-0.4.0.pom">
            <sha256 value="ef790f7cfe23da0b92aaa121847851fc3eea64535381f4965c68e71f3a7bb1c6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentracing.contrib" name="opentracing-okhttp-parent" version="3.0.0">
         <artifact name="opentracing-okhttp-parent-3.0.0.pom">
            <sha256 value="abfd7b351cbf03dd7b8b9fde4bb76dcfdddb3a60c4534e7d516f943d20f231e9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.opentracing.contrib" name="opentracing-okhttp3" version="3.0.0">
         <artifact name="opentracing-okhttp3-3.0.0.jar">
            <sha256 value="17006f0e7f6c360c6ac125ca4d22c1069a444202c602b352d542c4adef9462d6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentracing-okhttp3-3.0.0.pom">
            <sha256 value="3a7b2fb2d6c972ba08d45779136e2a11033ab647cbdb3aca8a483a477093e8b4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.perfmark" name="perfmark-api" version="0.27.0">
         <artifact name="perfmark-api-0.27.0.jar">
            <sha256 value="c7b478503ec524e55df19b424d46d27c8a68aeb801664fadd4f069b71f52d0f6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="perfmark-api-0.27.0.module">
            <sha256 value="9f6c4e6a62b8defd14173acdb7db293d086353b224923eef6293f58161893cca" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="perfmark-api-0.27.0.pom">
            <sha256 value="22c175c2c1823667630c84e73225767f5970492d8e6cbffb81a6575dba472d26" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.pkts" name="pkts-buffers" version="3.0.10">
         <artifact name="pkts-buffers-3.0.10.jar">
            <sha256 value="e60f26c4f39ef61dd7010619c50702f1f6d581342c53737b0cf0ecfd6a89d703" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="pkts-buffers-3.0.10.pom">
            <sha256 value="6d9685852ea6e74c3ce4b425aef96e9e435682c8ba8d89f5b1b4ef9b14fc6c8b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.pkts" name="pkts-core" version="3.0.10">
         <artifact name="pkts-core-3.0.10.jar">
            <sha256 value="4543debd3c96b4a3cce8a27c0f47501361107a8573605ae618c492570842682d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="pkts-core-3.0.10.pom">
            <sha256 value="e20fe11631b67c1a38f32d32492bc67ae4f4514f530737d5599204f5d2277d7f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.pkts" name="pkts-parent" version="3.0.10">
         <artifact name="pkts-parent-3.0.10.pom">
            <sha256 value="42067396968c5056e60ce65fef4f0714679a11564a5180a291d8ac72813ffb97" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.projectreactor" name="reactor-core" version="3.6.7">
         <artifact name="reactor-core-3.6.7.jar">
            <sha256 value="34d34caad35ab8153ff6e70e2e4634c035fee6af7bcae78f95418a488c0fbcda" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="reactor-core-3.6.7.module">
            <sha256 value="d401310298f4c346325a3b0725ff0d9c8d6f884e89fa7421f017633f6f6e71ca" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="reactor-core-3.6.7.pom">
            <sha256 value="5bf70db179616d067d416f5a779be9fc9e7cd736cfdb2bd82fb9eb245d44b150" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="client_java" version="1.3.5">
         <artifact name="client_java-1.3.5.pom">
            <sha256 value="9798404cc4dc70d792af1e4f0441cd29a1de7ad4e00407ca40036983b26ed662" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-bom" version="1.3.5">
         <artifact name="prometheus-metrics-bom-1.3.5.pom">
            <sha256 value="7ed9772d6079e32446fe1de8840424c2ae0d1d9b9a0ed771cec508049eca4120" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-config" version="1.3.5">
         <artifact name="prometheus-metrics-config-1.3.5.jar">
            <sha256 value="3999e82258d9b8280c32c9b5a1e83a8746da19988d563b40a026c249374fd8ba" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-config-1.3.5.pom">
            <sha256 value="0b21513b8fd2dc5a590529505b89676bc2cdd45d4a83d296db28b030b3754c77" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-core" version="1.3.5">
         <artifact name="prometheus-metrics-core-1.3.5.jar">
            <sha256 value="389c875fab0f62d56ae2efe061ee3cc0a1bf4fb37d5ce5d07afa6833aeb771bf" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-core-1.3.5.pom">
            <sha256 value="ed5efc48276d78407b74ca73b6d58311b826f953e5c03060ba55fcd8fd6817a9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-exporter-common" version="1.3.5">
         <artifact name="prometheus-metrics-exporter-common-1.3.5.jar">
            <sha256 value="14e4730d83150ac39b9676400b8b3ddcecd43c5721e0241a180d359782a5c6b8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-exporter-common-1.3.5.pom">
            <sha256 value="c55b81e77a513a3cdc7f368e33ffebe023dc1910488c6c8e82c35471e836be9f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-exporter-httpserver" version="1.3.5">
         <artifact name="prometheus-metrics-exporter-httpserver-1.3.5.jar">
            <sha256 value="30524f00e47ddd718b2daccb450196f04653eb96a4d44d07e3ebd44fd6a2a513" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-exporter-httpserver-1.3.5.pom">
            <sha256 value="94d23717c4fddadcf40e472a17d7abac941f1df46cbec85cf7c1eda9670278e9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-exporter-pushgateway" version="1.3.5">
         <artifact name="prometheus-metrics-exporter-pushgateway-1.3.5.jar">
            <sha256 value="3a0492ed41a318849af8bb85419ebed60960b6854923bc888b6d1f33b87c8d73" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-exporter-pushgateway-1.3.5.pom">
            <sha256 value="505e55045a038837d01da866b32f87cd299f23a703f34a777fb52ec5a18cd25d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-exposition-formats" version="1.3.5">
         <artifact name="prometheus-metrics-exposition-formats-1.3.5.jar">
            <sha256 value="c93022d645ebb0fa40fc8e9c6658abc06b69eae01d13f1d14b6f50ac7b296b08" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-exposition-formats-1.3.5.pom">
            <sha256 value="8f7b79766d59f2fbfe45edb4a9d1aa869a4e69843969f200742d3d163e4d072e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-exposition-textformats" version="1.3.5">
         <artifact name="prometheus-metrics-exposition-textformats-1.3.5.jar">
            <sha256 value="9090a4a7a6736a1305fc1cf555eb9a92ecea9610662cd45e3af81b2307bd00c3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-exposition-textformats-1.3.5.pom">
            <sha256 value="b4bebdf8b9e8e4cd55f59fcd16fdf0b502dce38c70c762fddb41e866e82e9a53" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-instrumentation-guava" version="1.3.5">
         <artifact name="prometheus-metrics-instrumentation-guava-1.3.5.jar">
            <sha256 value="a91fb15b065d5886b682d664e557b5bff967040eccf9b0c1912f9b12ec8df9a0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-instrumentation-guava-1.3.5.pom">
            <sha256 value="7eb70ebb7f38169e8267301d5265b2903ace0b3beddf1365940fdce6920befca" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-instrumentation-jvm" version="1.3.5">
         <artifact name="prometheus-metrics-instrumentation-jvm-1.3.5.jar">
            <sha256 value="5bc50f43f3a86e71a672e7ae41705b8ca564b84dad94ca1bc202aa8ea9f9c503" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-instrumentation-jvm-1.3.5.pom">
            <sha256 value="fe723e4c97630ecc0814198823fa93a45b6f253e13406136a92efb87d96083b2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-model" version="1.3.5">
         <artifact name="prometheus-metrics-model-1.3.5.jar">
            <sha256 value="431021dc649b59d2a7cfb356e593d9e8e7baabfff5a6976018c0a4b2711922dc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-model-1.3.5.pom">
            <sha256 value="4092e4a32119e84782347dbf4faf3e788c4690fdf7359728eb39bb46cd425009" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-tracer" version="1.3.5">
         <artifact name="prometheus-metrics-tracer-1.3.5.pom">
            <sha256 value="f26e8fb269da9710ab0e9c4a6326696799212913a72b4ba1660942a1f174a7d1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-tracer-common" version="1.3.5">
         <artifact name="prometheus-metrics-tracer-common-1.3.5.jar">
            <sha256 value="6c59d43356538fc8b16403b8acd74ff08f1434c77d95c43611ed4ef91b898889" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-tracer-common-1.3.5.pom">
            <sha256 value="152067923bdc8a285c990a1a6bdc9e649b1425fa5abcfaaba0db9c474d311a6a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-tracer-initializer" version="1.3.5">
         <artifact name="prometheus-metrics-tracer-initializer-1.3.5.jar">
            <sha256 value="c53e59936071742a797f99749e59846cb4efc2d06865431e88c4681e9d12113c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-tracer-initializer-1.3.5.pom">
            <sha256 value="6f744488d6c22cbe856fcb2188de91709d88364f321d38be0e0d80387f70ea2d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-tracer-otel" version="1.3.5">
         <artifact name="prometheus-metrics-tracer-otel-1.3.5.jar">
            <sha256 value="b201d5e1927609911b26061e820bf3841f3e9727eead4d7bceb5aa6ddb7a1ac9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-tracer-otel-1.3.5.pom">
            <sha256 value="5db7687eb65898cf8784d9f69e1d885c67d64ac837b58783d592a6842cba5117" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.prometheus" name="prometheus-metrics-tracer-otel-agent" version="1.3.5">
         <artifact name="prometheus-metrics-tracer-otel-agent-1.3.5.jar">
            <sha256 value="2ba5883fbbde051410ff4d1b8c29a6cd9f195fdfd240eabb57c5de89c916b96d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="prometheus-metrics-tracer-otel-agent-1.3.5.pom">
            <sha256 value="c626ac064a9d34335e55109d512c85c728fba83575290b097802052f10efae62" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.reactivex.rxjava2" name="rxjava" version="2.2.21">
         <artifact name="rxjava-2.2.21.jar">
            <sha256 value="59df6541a840018f0f4c899aae4f4c1f4383f4c16feb5268615fbe384d28501c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="rxjava-2.2.21.pom">
            <sha256 value="b25bece50483de9a3e1dfd25011c2d85330216ef8a81b86f6abebdbdf9b0f127" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-auth-common" version="4.5.13">
         <artifact name="vertx-auth-common-4.5.13.jar">
            <sha256 value="41e50b17ddc6e185aefda2864bab388cc1e58ad9cf7b660ed750974ef5399cb6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-auth-common-4.5.13.pom">
            <sha256 value="7f923292adb873ca7037da4bd788b0fee252e4456ee8f5142b6b461b56034ea0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-auth-jwt" version="4.5.13">
         <artifact name="vertx-auth-jwt-4.5.13.jar">
            <sha256 value="e1fa80cedd647c0846d761f024e11c1138805ba116e227697d891b94c4e4d558" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-auth-jwt-4.5.13.pom">
            <sha256 value="a6dcc274288784ee285962030f74e910324c73458e053dfe40bf8fd31e7e1d53" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-auth-parent" version="4.5.13">
         <artifact name="vertx-auth-parent-4.5.13.pom">
            <sha256 value="aea7e6fd3bc96f07d483409fc49e679ee2f0e2b6b55b187ebb0b1d1dd19ca7af" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-bridge-common" version="4.5.13">
         <artifact name="vertx-bridge-common-4.5.13.jar">
            <sha256 value="d93097bcf8bf91f0e1426b9fcee8ae3bb8d3835bfb58e3c09271d100cbf6c6a8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-bridge-common-4.5.13.pom">
            <sha256 value="fae3a4370ae4f45ad86d2d4b59a7965dd34aa96977597581fbcf77fa6317fe0a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-codegen" version="4.5.13">
         <artifact name="vertx-codegen-4.5.13.jar">
            <sha256 value="06a2df4c797a73f273303aae58faee81538ae143c84b13bb19f83366baef6e2f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-codegen-4.5.13.pom">
            <sha256 value="8eb6a418ff6c981b31f8916387f649c28894dbc20af77f10b4b2df31dec73734" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-core" version="4.5.11">
         <artifact name="vertx-core-4.5.11.jar">
            <sha256 value="8858c3c5299f859d0a076a1ba630e8a7d7065d6750a2f22b05b0e8916fb56b2d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-core-4.5.11.pom">
            <sha256 value="470d31ba48ab05451e3adf88825af87358e08b2432576a6aad223e29b92b69ca" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-core" version="4.5.13">
         <artifact name="vertx-core-4.5.13.jar">
            <sha256 value="fefa1b1f147c9a78dc1dd6eec870f4f95790ce9e43f50fa7a05cb8760c5ea6c6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-core-4.5.13.pom">
            <sha256 value="9369fabd95089f7ffbe4e11c270f7ef9c861aee2f59c93c1acfe711b968630be" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-dependencies" version="4.5.11">
         <artifact name="vertx-dependencies-4.5.11.pom">
            <sha256 value="cd99bcc60400f852f446661fb4df71c0fee9b129b644a407e8751ca2c5568878" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-dependencies" version="4.5.13">
         <artifact name="vertx-dependencies-4.5.13.pom">
            <sha256 value="fe6bcdf46e197e1eaae4dc3961acec28fe26f80e7f20cb970281480a5f81a407" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-ext" version="38">
         <artifact name="vertx-ext-38.pom">
            <sha256 value="9e00c39a0f0d832fde9f383c1e3ec7eb27d2bec15aa4c57d6bb1c639ab76a94c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-ext-parent" version="38">
         <artifact name="vertx-ext-parent-38.pom">
            <sha256 value="9ede6271ed5ee6c9987a808ca3cffddb732151f17ab71e5ee79674e74335317c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-junit5" version="4.5.13">
         <artifact name="vertx-junit5-4.5.13.jar">
            <sha256 value="24a74145f03c2c5f3c730da03552ce5b2b6ebdf8d6f8b681507450bb1536bf47" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-junit5-4.5.13.pom">
            <sha256 value="e10248187bdc624ef7de07c153ade3459cd0398b278b5b9b0c3253a82c3a050c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-parent" version="19">
         <artifact name="vertx-parent-19.pom">
            <sha256 value="219614cfbb9c31b6287672d3f615575eaa86c4cc26e402cfc87c8a753c16693e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-stack" version="4.5.13">
         <artifact name="vertx-stack-4.5.13.pom">
            <sha256 value="eb3392404276b46605601a3f48de21c14ae29a1af066d3d0b11a9a7aba4aaf62" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-stack-depchain" version="4.5.13">
         <artifact name="vertx-stack-depchain-4.5.13.pom">
            <sha256 value="05ed839e21866bbf551771d35eab6ec2afde1373bbea30667d9f2999382885d1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-unit" version="4.5.13">
         <artifact name="vertx-unit-4.5.13.jar">
            <sha256 value="6e9f675c140a2ff6c4fcb4142a33dbe3310f9c82c80cfc562cf7ac2517298fb3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-unit-4.5.13.pom">
            <sha256 value="74c5e783d5727ae51fbb93c273ce9b1c75048074c450f40f7feb6ece0a2347b5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-uri-template" version="4.5.13">
         <artifact name="vertx-uri-template-4.5.13.jar">
            <sha256 value="133da59ba80b693469d3875faa0bbca756c4d6025a89ac97ec1b450bb8535170" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-uri-template-4.5.13.pom">
            <sha256 value="46242b46d42307aef54a1b6f4ee503df2e25695ba88511da91943181d7f856ff" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-web" version="4.5.13">
         <artifact name="vertx-web-4.5.13.jar">
            <sha256 value="47cea077f323c27bba659cf3bd4131d16fb6c77cb76242a8b648f5f0b3f52ccd" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-web-4.5.13.pom">
            <sha256 value="3b516b1effe8a72cbb0295eec8a15cfd94cc3f227aa1d6917f1f39eb4aa51300" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-web-client" version="4.5.13">
         <artifact name="vertx-web-client-4.5.13.jar">
            <sha256 value="bd9ae65fe69c9c93b9d844363dc13f598377d00226825c43449706a824569b9b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-web-client-4.5.13.pom">
            <sha256 value="3110178a6b5499d6eac1db43781dc022213142c948970dce6d401b82b84b2355" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-web-common" version="4.5.13">
         <artifact name="vertx-web-common-4.5.13.jar">
            <sha256 value="6cf05394d297bd5556a6b7af37cb212b1ad82764f2ad8422c06523259a9ec4dc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="vertx-web-common-4.5.13.pom">
            <sha256 value="b8f320cf21f096231292950a201faa8e11b2389fa350ebfcd4ba1e63b042dd7f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="io.vertx" name="vertx-web-parent" version="4.5.13">
         <artifact name="vertx-web-parent-4.5.13.pom">
            <sha256 value="5aa1070db376df026caada281ad2e491ec2449ad843b49dccd0090ff46c869e1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="jakarta.annotation" name="jakarta.annotation-api" version="2.1.1">
         <artifact name="jakarta.annotation-api-2.1.1.jar">
            <sha256 value="5f65fdaf424eee2b55e1d882ba9bb376be93fb09b37b808be6e22e8851c909fe" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jakarta.annotation-api-2.1.1.pom">
            <sha256 value="af650eca1de1b98741006acd82507e4408cffedd2fee33a0ea463d61508db7ec" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="jakarta.inject" name="jakarta.inject-api" version="2.0.1">
         <artifact name="jakarta.inject-api-2.0.1.jar">
            <sha256 value="f7dc98062fccf14126abb751b64fab12c312566e8cbdc8483598bffcea93af7c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jakarta.inject-api-2.0.1.pom">
            <sha256 value="e7fd7232e96307a575b2494c9367d68cf43ec98244aace3ccc23e1773ffa6fda" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="jakarta.platform" name="jakarta.jakartaee-bom" version="9.1.0">
         <artifact name="jakarta.jakartaee-bom-9.1.0.pom">
            <sha256 value="df98e0266219fdbb82562826d79a3a20776a8ba02aa787f0f0765a538654c8a4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="jakarta.platform" name="jakartaee-api-parent" version="9.1.0">
         <artifact name="jakartaee-api-parent-9.1.0.pom">
            <sha256 value="a7702c487026802784b5797b62330a8b8d6592bf0f4737b25c67a5eac82659c0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="jakarta.validation" name="jakarta.validation-api" version="3.0.2">
         <artifact name="jakarta.validation-api-3.0.2.jar">
            <sha256 value="291c25e6910cc6a7ebd96d4c6baebf6d7c37676c5482c2d96146e901b62c1fc9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jakarta.validation-api-3.0.2.pom">
            <sha256 value="0a7b9c6327968afe0d82f7cf4facbda7e157968c5808d69bcb8019863b298dc4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="javax.inject" name="javax.inject" version="1">
         <artifact name="javax.inject-1.jar">
            <sha256 value="91c77044a50c481636c32d916fd89c9118a72195390452c81065080f957de7ff" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="javax.inject-1.pom">
            <sha256 value="943e12b100627804638fa285805a0ab788a680266531e650921ebfe4621a8bfa" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="junit" name="junit" version="4.12">
         <artifact name="junit-4.12.jar">
            <sha256 value="59721f0805e223d84b90677887d9ff567dc534d7c502ca903c0c2b17f05c116a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-4.12.pom">
            <sha256 value="90f163f78e3ffb6f1c7ad97de9e7eba4eea25807141b85d6d12be67ca25449c4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="junit" name="junit" version="4.13.2">
         <artifact name="junit-4.13.2.jar">
            <sha256 value="8e495b634469d64fb8acfa3495a065cbacc8a0fff55ce1e31007be4c16dc57d3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-4.13.2.pom">
            <sha256 value="569b6977ee4603c965c1c46c3058fa6e969291b0160eb6964dd092cd89eadd94" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="me.champeau.jmh" name="jmh-gradle-plugin" version="0.7.3">
         <artifact name="jmh-gradle-plugin-0.7.3.jar">
            <sha256 value="d7097e619541d90e0a970b2a68573e22ad01d2999ee5365d56d59830765bf98f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jmh-gradle-plugin-0.7.3.module">
            <sha256 value="3487d1aba24fe0af527c6d5f78b5f0e8fd64fe9878708b460e6600e39a47bc43" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="me.champeau.jmh" name="me.champeau.jmh.gradle.plugin" version="0.7.3">
         <artifact name="me.champeau.jmh.gradle.plugin-0.7.3.pom">
            <sha256 value="d516226b3b114e4b32d42544d1d2796c732c5465d5dae7cc846be6b23bed8d1d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="net.bytebuddy" name="byte-buddy" version="1.15.11">
         <artifact name="byte-buddy-1.15.11.jar">
            <sha256 value="fa08998aae1e7bdae83bde0712c50e8444d71c0e0c196bb2247ade8d4ad0eb90" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="byte-buddy-1.15.11.pom">
            <sha256 value="205b8b254196717e81dad672bb869a719afc96df297f97d811effe1f43653dae" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="net.bytebuddy" name="byte-buddy-agent" version="1.15.11">
         <artifact name="byte-buddy-agent-1.15.11.jar">
            <sha256 value="316d2c0795c2a4d4c4756f2e6f9349837c7430ac34e0477ead874d05f5cc19e5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="byte-buddy-agent-1.15.11.pom">
            <sha256 value="b5fa1396f14797b8d808827e7743ba8a5f203b4889be32e4963d44bd5ed759a8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="net.bytebuddy" name="byte-buddy-parent" version="1.15.11">
         <artifact name="byte-buddy-parent-1.15.11.pom">
            <sha256 value="8dc519d7a3e792112a7cd841eafbec4f00dbc633d085add20c45f0ab476ca496" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="net.java.dev.jna" name="jna" version="5.16.0">
         <artifact name="jna-5.16.0.jar">
            <sha256 value="3f5233589a799eb66dc2969afa3433fb56859d3d787c58b9bc7dd9e86f0a250c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jna-5.16.0.pom">
            <sha256 value="f61fd2c44aa583f2a2cbc5fc67b0f19a9203ca87d5f0e18d3d5030cbe3908083" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="net.java.dev.jna" name="jna-platform" version="5.14.0">
         <artifact name="jna-platform-5.14.0.jar">
            <sha256 value="ae4caceb3840730c2537f9b7fb55a01baba580286b4122951488bcee558c2449" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jna-platform-5.14.0.pom">
            <sha256 value="6cba0e04f9eecb16486000793bb27e103b0f4d0485e0554e2b4c0aed13d2ed16" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="net.java.dev.jna" name="jna-platform" version="5.16.0">
         <artifact name="jna-platform-5.16.0.jar">
            <sha256 value="e5a79523964509757555782bb60283e4902611013f107e4600dc93298f73f382" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jna-platform-5.16.0.pom">
            <sha256 value="477793df02c6827dfe01bdb08f006a55775e6fa052dc4acdeda34c993628a496" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="net.ltgt.errorprone" name="net.ltgt.errorprone.gradle.plugin" version="4.2.0">
         <artifact name="net.ltgt.errorprone.gradle.plugin-4.2.0.pom">
            <sha256 value="7a633c2a4ef49437c4ae26b01c03d6e398718549b7b86bdc5a328915bc827bd1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="net.ltgt.gradle" name="gradle-errorprone-plugin" version="4.2.0">
         <artifact name="gradle-errorprone-plugin-4.2.0.jar">
            <sha256 value="e731792b8cc7fbac9e6dac4cce29fcbac67c83f77e70582b78763c3adaab57e8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="gradle-errorprone-plugin-4.2.0.module">
            <sha256 value="bebbba6574c19c4087f651911b1b84d433dd50d00b41e75136163777b40f61db" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="net.ltgt.gradle.incap" name="incap" version="0.2">
         <artifact name="incap-0.2.jar">
            <sha256 value="b625b9806b0f1e4bc7a2e3457119488de3cd57ea20feedd513db070a573a4ffd" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="incap-0.2.pom">
            <sha256 value="1a4a08a1e88d32052cd82dc2f740b34d3048e2c0e6a7c2bfe2309ed00771f73a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="net.sf.jopt-simple" name="jopt-simple" version="5.0.4">
         <artifact name="jopt-simple-5.0.4.jar">
            <sha256 value="df26cc58f235f477db07f753ba5a3ab243ebe5789d9f89ecf68dd62ea9a66c28" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jopt-simple-5.0.4.pom">
            <sha256 value="6a67763b76afcd9c80b95e5c5e24782d18cc1b0e3d9b454ad3f8754c76b76815" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.abego.treelayout" name="org.abego.treelayout.core" version="1.0.3">
         <artifact name="org.abego.treelayout.core-1.0.3.jar">
            <sha256 value="fa5e31395c39c2e7d46aca0f81f72060931607b2fa41bd36038eb2cb6fb93326" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.abego.treelayout.core-1.0.3.pom">
            <sha256 value="a3b2b223794370355e792433af012fc993667c0331be2bacad84dbc09ace4a0c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.antlr" name="ST4" version="4.3.4">
         <artifact name="ST4-4.3.4.jar">
            <sha256 value="f927ac384c46d749f8b5ec68972a53aed21e00313509299616edb73bfa15ff33" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="ST4-4.3.4.pom">
            <sha256 value="9e7c1f3e489919440e8c13089e5963729d5b7f80f70233bfbae309c649abca3e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.antlr" name="antlr-master" version="3.5.3">
         <artifact name="antlr-master-3.5.3.pom">
            <sha256 value="ea9e37250f5c4c2e76b6538be97b57f3349bda585edf53f3ca97e207b385b895" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.antlr" name="antlr-runtime" version="3.5.3">
         <artifact name="antlr-runtime-3.5.3.jar">
            <sha256 value="68bf9f5a33dfcb34033495c587e6236bef4e37aa6612919f5b1e843b90669fb9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="antlr-runtime-3.5.3.pom">
            <sha256 value="13298e0e0aafaf414ff7d4406427cab6ec4fbfa36427f6d7103c432f32c07d25" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.antlr" name="antlr4" version="4.13.0">
         <artifact name="antlr4-4.13.0.jar">
            <sha256 value="1c0deb26992514922fb8b99619198f09c32f7984246dc6ad1c3bbc4983de1d35" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="antlr4-4.13.0.pom">
            <sha256 value="39d2d258493c427bcbd440063f7e0f42ab788fac15a78c0fef744ae6193677c9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.antlr" name="antlr4-master" version="4.11.1">
         <artifact name="antlr4-master-4.11.1.pom">
            <sha256 value="72ea5de8dabb6615785fd0feaaababd53dcdae70fe16bcd7c7b9686c0a6e00ad" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.antlr" name="antlr4-master" version="4.13.0">
         <artifact name="antlr4-master-4.13.0.pom">
            <sha256 value="22206fd7ba495152e526f50efec9fc8f4dd05fcb43dfcf8f264e837df6b6424f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.antlr" name="antlr4-runtime" version="4.11.1">
         <artifact name="antlr4-runtime-4.11.1.pom">
            <sha256 value="c456ec2959078c591fbd7ef69ad9400a727920068d746989c74ab804ed681b34" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.antlr" name="antlr4-runtime" version="4.13.0">
         <artifact name="antlr4-runtime-4.13.0.jar">
            <sha256 value="bd7f7b5d07bc0b047f10915b32ca4bb1de9e57d8049098882e4453c88c076a5d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="antlr4-runtime-4.13.0.pom">
            <sha256 value="198e34fb5ac7597b1a3c31930301e08ce941e5ca504116dd54a3a75378914a7f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="13">
         <artifact name="apache-13.pom">
            <sha256 value="ff513db0361fd41237bef4784968bc15aae478d4ec0a9496f811072ccaf3841d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="16">
         <artifact name="apache-16.pom">
            <sha256 value="9f85ff2fd7d6cb3097aa47fb419ee7f0ebe869109f98aba9f4eca3f49e74a40e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="19">
         <artifact name="apache-19.pom">
            <sha256 value="91f7a33096ea69bac2cbaf6d01feb934cac002c48d8c8cfa9c240b40f1ec21df" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="21">
         <artifact name="apache-21.pom">
            <sha256 value="af10c108da014f17cafac7b52b2b4b5a3a1c18265fa2af97a325d9143537b380" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="23">
         <artifact name="apache-23.pom">
            <sha256 value="bc10624e0623f36577fac5639ca2936d3240ed152fb6d8d533ab4d270543491c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="27">
         <artifact name="apache-27.pom">
            <sha256 value="b2b0fc69e22a650c3892f1c366d77076f29575c6738df4c7a70a44844484cdf9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="29">
         <artifact name="apache-29.pom">
            <sha256 value="3e49037174820bbd0df63420a977255886398954c2a06291fa61f727ac35b377" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="30">
         <artifact name="apache-30.pom">
            <sha256 value="63dd4a393a9c0dfcb314efe83871a41d243bc8d200cbc7f2d197f30da78239d8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="31">
         <artifact name="apache-31.pom">
            <sha256 value="555d0c9eaa69c042aff924927b9381e8f8174136d355eead445224452e6291cc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="32">
         <artifact name="apache-32.pom">
            <sha256 value="cfd872c0ec27f53ae68f43dbc0fecded8add773079a53afbd390e407b42ce72f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="33">
         <artifact name="apache-33.pom">
            <sha256 value="d78bd8524c5f8380a190a6525686629a95dfe512df21111383a6d8c0923a4415" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="4">
         <artifact name="apache-4.pom">
            <sha256 value="9e9323a26ba8eb2394efef0c96d31b70df570808630dc147cab1e73541cc5194" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache" name="apache" version="7">
         <artifact name="apache-7.pom">
            <sha256 value="1397ce1db433adc9f223dbf07496d133681448751f4ae29e58f68e78fb4b6c25" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-collections4" version="4.4">
         <artifact name="commons-collections4-4.4.jar">
            <sha256 value="1df8b9430b5c8ed143d7815e403e33ef5371b2400aadbe9bda0883762e0846d1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-collections4-4.4.pom">
            <sha256 value="271bd673839af46e73aff957e2918d4bf96f5ac4f6c6cf4d5be93fd1f1271c4d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-compress" version="1.26.2">
         <artifact name="commons-compress-1.26.2.pom">
            <sha256 value="1428719895cd0a913aee33e2424b9a804b10f1197e5d35a9ce99cf2a1174cb0e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-compress" version="1.27.1">
         <artifact name="commons-compress-1.27.1.jar">
            <sha256 value="293d80f54b536b74095dcd7ea3cf0a29bbfc3402519281332495f4420d370d16" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-compress-1.27.1.pom">
            <sha256 value="df8cc1a8387d4ce842363b7209fdc6d35df9763839fd3fcab558a0f83f9d841c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-lang3" version="3.12.0">
         <artifact name="commons-lang3-3.12.0.pom">
            <sha256 value="82d31f1dcc4583effd744e979165b16da64bf86bca623fc5d1b03ed94f45c85a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-lang3" version="3.14.0">
         <artifact name="commons-lang3-3.14.0.jar">
            <sha256 value="7b96bf3ee68949abb5bc465559ac270e0551596fa34523fddf890ec418dde13c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-lang3-3.14.0.pom">
            <sha256 value="110438863bad37c28f906bf87016e38c7a8c758ba321e09d11dc5a2363a8e79e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-lang3" version="3.17.0">
         <artifact name="commons-lang3-3.17.0.jar">
            <sha256 value="6ee731df5c8e5a2976a1ca023b6bb320ea8d3539fbe64c8a1d5cb765127c33b4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-lang3-3.17.0.pom">
            <sha256 value="351c6e4940e939b1f330df47f60f13ba383db81ee008181af541f3a2a6d2a56c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-math3" version="3.2">
         <artifact name="commons-math3-3.2.jar">
            <sha256 value="6268a9a0ea3e769fc493a21446664c0ef668e48c93d126791f6f3f757978fee2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-math3-3.2.pom">
            <sha256 value="2cd0db7bce370c1404025cc013c11f8fd49f3f3c340a6d2dcf99d363d7948a69" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-math3" version="3.6.1">
         <artifact name="commons-math3-3.6.1.jar">
            <sha256 value="1e56d7b058d28b65abd256b8458e3885b674c1d588fa43cd7d1cbb9c7ef2b308" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-math3-3.6.1.pom">
            <sha256 value="fad72336ea7d7dd06da103144e3740db508fa4b17d9c54d7847737edc24a7e60" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="11">
         <artifact name="commons-parent-11.pom">
            <sha256 value="b9e0306f393460105b8a3fa5105250c5291b1efaa99954ace0ec1c783109a02a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="28">
         <artifact name="commons-parent-28.pom">
            <sha256 value="14733a68e8b120b69de60cd96d222146dcf32f03c1c6cc6a750b1269bafe86c7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="34">
         <artifact name="commons-parent-34.pom">
            <sha256 value="3a2e69d06d641d1f3b293126dc9e2e4ea6563bf8c36c87e0ab6fa4292d04b79c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="39">
         <artifact name="commons-parent-39.pom">
            <sha256 value="87cd27e1a02a5c3eb6d85059ce98696bb1b44c2b8b650f0567c86df60fa61da7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="47">
         <artifact name="commons-parent-47.pom">
            <sha256 value="8a8ecb570553bf9f1ffae211a8d4ca9ee630c17afe59293368fba7bd9b42fcb7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="48">
         <artifact name="commons-parent-48.pom">
            <sha256 value="1e1f7de9370a7b7901f128f1dacd1422be74e3f47f9558b0f79e04c0637ca0b4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="52">
         <artifact name="commons-parent-52.pom">
            <sha256 value="75dbe8f34e98e4c3ff42daae4a2f9eb4cbcd3b5f1047d54460ace906dbb4502e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="54">
         <artifact name="commons-parent-54.pom">
            <sha256 value="000d8187952b223702fde296df799563f35de82ce72adb4e7bf157342378fbe3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="58">
         <artifact name="commons-parent-58.pom">
            <sha256 value="2d4b12e18899063abd7c75278b5fa97a3729d80878ceecb6a40d946e9c0d5590" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="64">
         <artifact name="commons-parent-64.pom">
            <sha256 value="6f19638994e8357b4ed734696f992057efaafa1235673998133299798e2ccddb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="65">
         <artifact name="commons-parent-65.pom">
            <sha256 value="6cf3495fc2e6ac913a2b7f2e03fb5908fb3f229fb06d3358dc45678d5af3e36e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="69">
         <artifact name="commons-parent-69.pom">
            <sha256 value="d50da9c39bdca823d618d1b4a03b73f196497fcb8616fd0da727c8623592a9bb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="70">
         <artifact name="commons-parent-70.pom">
            <sha256 value="60335a34e91f49ce50723b007d7c12514fd5766da17dfffb6732e1131e58cd1b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="71">
         <artifact name="commons-parent-71.pom">
            <sha256 value="95b7be70f316ae4ca22f6fbdd08de2182e87cd874a650de7c3d3386a747a82a3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="72">
         <artifact name="commons-parent-72.pom">
            <sha256 value="4345debfc767b1aeac68abdd72fc67d18b521d4b390372a11b63ff0c586b2320" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="73">
         <artifact name="commons-parent-73.pom">
            <sha256 value="4ed44560b07f8448479dfd1e83a422ba4e83e60b36e51b2871ac502a6d5c1bea" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-parent" version="78">
         <artifact name="commons-parent-78.pom">
            <sha256 value="022d202e655edd04f2a10ecbe453d92977924d38380a4ca8c359f1817a80320e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.commons" name="commons-text" version="1.13.0">
         <artifact name="commons-text-1.13.0.jar">
            <sha256 value="1e323a501127df78ed0987f345d69d65d0ea7fa3d4fb5b3f84aaeba3a8b20f38" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="commons-text-1.13.0.pom">
            <sha256 value="d46614bdca6d1195c333ba9f730cd08585c4f332a43ea7b90b6035ad804871d8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.felix" name="felix-parent" version="8">
         <artifact name="felix-parent-8.pom">
            <sha256 value="c062d00b93de05098e08e308d60792ae5dd2a41d1711a07dc2853b9c22b0d8b8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.felix" name="org.apache.felix.scr" version="2.2.10">
         <artifact name="org.apache.felix.scr-2.2.10.jar">
            <sha256 value="dd9d920101890ecd0565a8e48bf3f80bbe3d398cdf0f1970a19eec4f54c8d4e1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.apache.felix.scr-2.2.10.pom">
            <sha256 value="2b2c0d8fc392750cd2b4d6b25ac5a8306845bf90df035a2c75ac5ded458f2dec" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.groovy" name="groovy-bom" version="4.0.22">
         <artifact name="groovy-bom-4.0.22.module">
            <sha256 value="525d3f486bc0adf16f37e6002fd465ab2802a5bda5f4c6567fbefc728a68e666" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="groovy-bom-4.0.22.pom">
            <sha256 value="1e1f6b4222ae7bfd6332003edf70201835990dbd46106b16cddba8a53e3cdf65" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.httpcomponents" name="httpclient" version="4.5.13">
         <artifact name="httpclient-4.5.13.jar">
            <sha256 value="6fe9026a566c6a5001608cf3fc32196641f6c1e5e1986d1037ccdbd5f31ef743" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="httpclient-4.5.13.pom">
            <sha256 value="78eb9ada74929fcd63d07adc4f49236841a45cc29d5f817bf45801f513fd7e6c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.httpcomponents" name="httpclient" version="4.5.14">
         <artifact name="httpclient-4.5.14.jar">
            <sha256 value="c8bc7e1c51a6d4ce72f40d2ebbabf1c4b68bfe76e732104b04381b493478e9d6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="httpclient-4.5.14.pom">
            <sha256 value="f18355af4cf80a8a4ef04ebd742a47e90a7eaf080c725b2095dbc4fc5dbdefb7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.httpcomponents" name="httpcomponents-client" version="4.5.13">
         <artifact name="httpcomponents-client-4.5.13.pom">
            <sha256 value="9cba594c08db7271d0c20e9845d622bb39e69583910b45e7d5df82f6058d4dd9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.httpcomponents" name="httpcomponents-client" version="4.5.14">
         <artifact name="httpcomponents-client-4.5.14.pom">
            <sha256 value="5bad1de4f101447659f89d089868ccbad64a68cc503d2d65410b51f6904aa061" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.httpcomponents" name="httpcomponents-core" version="4.4.16">
         <artifact name="httpcomponents-core-4.4.16.pom">
            <sha256 value="f2d75a2c2d423ad18539bf21656d56f88a4091944a662fcaf159d5ae283db7f7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.httpcomponents" name="httpcomponents-parent" version="11">
         <artifact name="httpcomponents-parent-11.pom">
            <sha256 value="a901f87b115c55070c7ee43efff63e20e7b02d30af2443ae292bf1f4e532d3aa" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.httpcomponents" name="httpcore" version="4.4.16">
         <artifact name="httpcore-4.4.16.jar">
            <sha256 value="6c9b3dd142a09dc468e23ad39aad6f75a0f2b85125104469f026e52a474e464f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="httpcore-4.4.16.pom">
            <sha256 value="3cbad849b35dacfe6cec31adada2c623c026c3261141b0d26eec7e399c6cd7fa" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.ivy" name="ivy" version="2.5.2">
         <artifact name="ivy-2.5.2.jar">
            <sha256 value="98428d545ea63cd9a0aaf255caf42cb8cb64fe430dbb5e709aed536d4daeed04" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="ivy-2.5.2.pom">
            <sha256 value="4ab2161fade3d841e9d87f2255f7607cb88709c6c848f7fb25d06691dd956e44" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging" name="logging-parent" version="10.6.0">
         <artifact name="logging-parent-10.6.0.pom">
            <sha256 value="f827475840a64083b585ec8dbbf7093b6fd02624293cec37d56edf9fc354109e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging" name="logging-parent" version="11.3.0">
         <artifact name="logging-parent-11.3.0.pom">
            <sha256 value="a5c985b56fe1c58433393b5091a6f39e5b9f78518dd8fc92134690599b64d7d0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging" name="logging-parent" version="3">
         <artifact name="logging-parent-3.pom">
            <sha256 value="763a2ec2b8094d4161deb6c264b1262085b9be30bf3a31c2ce137242e57722a7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j" version="2.17.1">
         <artifact name="log4j-2.17.1.pom">
            <sha256 value="967abc024443a9cb096935559af5e9ad6f0ff61375f84b27d440d2fa70806b09" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j" version="2.23.1">
         <artifact name="log4j-2.23.1.pom">
            <sha256 value="6ce1540455364b53ea22a18342cb126a6381633f653f095467f2aa198511a6ce" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j" version="2.24.3">
         <artifact name="log4j-2.24.3.pom">
            <sha256 value="c141b4863fc0ceab583894a13e1f9e52c01fc2d758727d6747f6b99d5040f3b1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j-api" version="2.17.1">
         <artifact name="log4j-api-2.17.1.pom">
            <sha256 value="1e2acef3220b29be10ae099728b158b18d943f91a193cc4501bb42f929c1e88a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j-api" version="2.23.1">
         <artifact name="log4j-api-2.23.1.pom">
            <sha256 value="b67cc3d0980927049a3beb00d506ece288746732b0dec812245c59f453fb1ca1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j-api" version="2.24.3">
         <artifact name="log4j-api-2.24.3.jar">
            <sha256 value="5b4a0a0cd0e751ded431c162442bdbdd53328d1f8bb2bae5fc1bbeee0f66d80f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="log4j-api-2.24.3.pom">
            <sha256 value="bc05de33533a1259adbacbfcc826cd66376a2d9c4ee53fb836009f44a45b8239" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j-bom" version="2.23.1">
         <artifact name="log4j-bom-2.23.1.pom">
            <sha256 value="372396e0458d4cd32c096cadabe0cc9330ec10f4f57fa3be2e74f79b5e178a35" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j-bom" version="2.24.3">
         <artifact name="log4j-bom-2.24.3.pom">
            <sha256 value="b17ab7f328f4586b7e71f8c94fc35a5da2bce80705a5d63004022c1928833558" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j-core" version="2.17.1">
         <artifact name="log4j-core-2.17.1.pom">
            <sha256 value="0bbb3bf6d4d22a1bfa3c3c0927c29410af14a0fb26e3b02b93726f5c7e98aafd" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j-core" version="2.23.1">
         <artifact name="log4j-core-2.23.1.pom">
            <sha256 value="c89ba0c23757511b9252b27a731d01e863b28ddfce503e7dee94ae2318c82ddc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j-core" version="2.24.3">
         <artifact name="log4j-core-2.24.3.jar">
            <sha256 value="7eb4084596ae25bd3c61698e48e8d0ab65a9260758884ed5cbb9c6e55c44a56a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="log4j-core-2.24.3.pom">
            <sha256 value="bfd5c0c4aac610242ccb61ff00108ad73780da20a2f729be063836a976577f57" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j-jul" version="2.24.3">
         <artifact name="log4j-jul-2.24.3.jar">
            <sha256 value="e76b925cd6e70a82364a8059e06db30312b74689ff4650104a755baff93710d5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="log4j-jul-2.24.3.pom">
            <sha256 value="736b5b2c4bf4514baaacefa1dbbb06505fa08a096210732feeba0332f63cb227" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.logging.log4j" name="log4j-slf4j2-impl" version="2.24.3">
         <artifact name="log4j-slf4j2-impl-2.24.3.jar">
            <sha256 value="cdaac22e40ec30c4096e1ebe8c454c8826c0d1c378d7db5d7b3ad166354b0bd3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="log4j-slf4j2-impl-2.24.3.pom">
            <sha256 value="7cc96855d75ddb84562e3507334905d0d3530c40adb720801939881e093738a3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.maven" name="maven" version="3.6.3">
         <artifact name="maven-3.6.3.pom">
            <sha256 value="d2d86245ea66149bc14d2dd72bbb961f964dd658b809a0573252c06531eeec16" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.maven" name="maven" version="3.9.9">
         <artifact name="maven-3.9.9.pom">
            <sha256 value="d24ed68923df210d085bb5d386d4336d12ede5473392e159d5e6e64497af6f39" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.maven" name="maven-artifact" version="3.9.9">
         <artifact name="maven-artifact-3.9.9.jar">
            <sha256 value="30f015d1c1a393e19c18cd4f43532089c36d4ca328608ce3dda78b74d3d31515" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="maven-artifact-3.9.9.pom">
            <sha256 value="65ddf76b92a707b9f1240eccbb4774b30d4c77778671291fd355e06d4d3a96df" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.maven" name="maven-parent" version="33">
         <artifact name="maven-parent-33.pom">
            <sha256 value="3856e3fcd169502d5f12fe2452604ebf6c7c025f15656bfa558ea99ed29d73ea" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apache.maven" name="maven-parent" version="43">
         <artifact name="maven-parent-43.pom">
            <sha256 value="468a1262e9aaa5febf9d604e2836267f815351f2b4520f28fb17f53c71e33554" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apiguardian" name="apiguardian-api" version="1.1.0">
         <artifact name="apiguardian-api-1.1.0.jar">
            <sha256 value="a9aae9ff8ae3e17a2a18f79175e82b16267c246fbbd3ca9dfbbb290b08dcfdd4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="apiguardian-api-1.1.0.pom">
            <sha256 value="a945b9cb5cd9b77b2c711844e659c43ec070ef59d9f509fa9f4c1861b4862711" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.apiguardian" name="apiguardian-api" version="1.1.2">
         <artifact name="apiguardian-api-1.1.2.jar">
            <sha256 value="b509448ac506d607319f182537f0b35d71007582ec741832a1f111e5b5b70b38" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="apiguardian-api-1.1.2.module">
            <sha256 value="e08028131375b357d1d28734e9a4fb4216da84b240641cb3ef7e7c7d628223fc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.assertj" name="assertj-bom" version="3.27.3">
         <artifact name="assertj-bom-3.27.3.pom">
            <sha256 value="408f9e3816661817b1479bde3b2fc9dd95161562ccfa79778920e62b7f2451be" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.assertj" name="assertj-core" version="3.27.3">
         <artifact name="assertj-core-3.27.3.jar">
            <sha256 value="5b8a26205f6d5ea60ad9ce65ce4a40a2afe4c48abeec61bd0740a088c24e89f5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="assertj-core-3.27.3.pom">
            <sha256 value="8eb37e416b7807e7bff37dd037c40c06b95693a76059c5fb9beb8549a4ced7dc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.awaitility" name="awaitility" version="4.3.0">
         <artifact name="awaitility-4.3.0.jar">
            <sha256 value="ee58568ea5945dcf988551501655183dc184e23e45a8e013fdfd9036194e6f7b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="awaitility-4.3.0.pom">
            <sha256 value="ec817516c83141a33f695ff8f4d73ca6df3af268c3ab377e5cee7effccca8140" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.awaitility" name="awaitility-parent" version="4.3.0">
         <artifact name="awaitility-parent-4.3.0.pom">
            <sha256 value="7af9ce8a09581211557e6cc8e2909bd80666b34e922a28587c773096c1022093" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.bouncycastle" name="bcpg-jdk18on" version="1.77">
         <artifact name="bcpg-jdk18on-1.77.jar">
            <sha256 value="07ae5704f3b729284b646643c798db0984af0126e646233fbd577b34de69fdf2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="bcpg-jdk18on-1.77.pom">
            <sha256 value="b0b19e34a11361b600fa06a76e5e69b758d4c11cc9d1ff4f2fa4bbc3d2175c12" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.bouncycastle" name="bcpkix-jdk18on" version="1.80">
         <artifact name="bcpkix-jdk18on-1.80.jar">
            <sha256 value="4f4ba6a92617ea19dc183f0fa5db492eee426fdde2a0a2d6c94777ffd1af6413" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="bcpkix-jdk18on-1.80.pom">
            <sha256 value="a4a122113467b728e18f26fb0cfd57f0b1a0d7c4a53b86718ace70bf8b86ed47" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.bouncycastle" name="bcprov-jdk18on" version="1.77">
         <artifact name="bcprov-jdk18on-1.77.jar">
            <sha256 value="dabb98c24d72c9b9f585633d1df9c5cd58d9ad373d0cd681367e6a603a495d58" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="bcprov-jdk18on-1.77.pom">
            <sha256 value="ad1382cfcd03bcdd8be13913c02f44fd469d0a76a53cf2caef5be180adc36b23" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.bouncycastle" name="bcprov-jdk18on" version="1.78.1">
         <artifact name="bcprov-jdk18on-1.78.1.jar">
            <sha256 value="add5915e6acfc6ab5836e1fd8a5e21c6488536a8c1f21f386eeb3bf280b702d7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="bcprov-jdk18on-1.78.1.pom">
            <sha256 value="28912d139f9eed141c394371fbe5ba6ffff91e78f1c9cb8348f944a24d204ed2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.bouncycastle" name="bcprov-jdk18on" version="1.80">
         <artifact name="bcprov-jdk18on-1.80.jar">
            <sha256 value="e8ad209f8c58d291a37ca9750e9e9fac60596956c983e49dd8282381dd8b3249" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="bcprov-jdk18on-1.80.pom">
            <sha256 value="a0a75c76d91c421eea56d0f6062fb8f63edf7fac7ec724fd420ccdcad7181d43" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.bouncycastle" name="bcutil-jdk18on" version="1.80">
         <artifact name="bcutil-jdk18on-1.80.jar">
            <sha256 value="22eca687f7955411f456af33e6ea8e68fc73cd80cb8b32aa5f7a8b1827d7c678" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="bcutil-jdk18on-1.80.pom">
            <sha256 value="421a7de4bfeb9c5b38b1fc47c426a087d90878955d4107f5b7a82eb1d7b747b6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.checkerframework" name="checker-compat-qual" version="2.5.5">
         <artifact name="checker-compat-qual-2.5.5.jar">
            <sha256 value="11d134b245e9cacc474514d2d66b5b8618f8039a1465cdc55bbc0b34e0008b7a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="checker-compat-qual-2.5.5.pom">
            <sha256 value="42f21ebd9183be049ee5afc822b345403a5da764037875734a039b0d6e0353be" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.checkerframework" name="checker-qual" version="3.37.0">
         <artifact name="checker-qual-3.37.0.jar">
            <sha256 value="e4ce1376cc2735e1dde220b62ad0913f51297704daad155a33f386bc5db0d9f7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="checker-qual-3.37.0.module">
            <sha256 value="7258a769dcaa26b98154d229d85cc72e5b3666b0bcb637d2daf16ec498956638" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.checkerframework" name="checker-qual" version="3.43.0">
         <artifact name="checker-qual-3.43.0.jar">
            <sha256 value="3fbc2e98f05854c3df16df9abaa955b91b15b3ecac33623208ed6424640ef0f6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="checker-qual-3.43.0.module">
            <sha256 value="f8163327245ab8625532948c72a930548cd97f34d6c3fe860fa6aec5a34d79b4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="checker-qual-3.43.0.pom">
            <sha256 value="9313bf53b3efd8aaca266eea8b96e307976b65c0b16510cc6f02319fbaebed43" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.codehaus.groovy" name="groovy-bom" version="3.0.21">
         <artifact name="groovy-bom-3.0.21.pom">
            <sha256 value="92cc36affd20f568b5092c0b94ecf585ddeb0a281b6c8ba7256570bb185d6534" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.codehaus.mojo" name="animal-sniffer-annotations" version="1.24">
         <artifact name="animal-sniffer-annotations-1.24.jar">
            <sha256 value="c720e6e5bcbe6b2f48ded75a47bccdb763eede79d14330102e0d352e3d89ed92" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="animal-sniffer-annotations-1.24.pom">
            <sha256 value="88484f60a6ad4238a97febacf2b333e9e08c178b8f180a05a3edbff4a38a836e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.codehaus.mojo" name="animal-sniffer-parent" version="1.24">
         <artifact name="animal-sniffer-parent-1.24.pom">
            <sha256 value="49ddab43c8361dc2ef0c1ff87cb590fa7231702ab9f62e26d512dc18a1f1cd04" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.codehaus.mojo" name="mojo-parent" version="84">
         <artifact name="mojo-parent-84.pom">
            <sha256 value="2fe510618b2f60fcd5f2fb82bc4b2c2c344048d74f30be719fcb86829ee8ac30" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.codehaus.plexus" name="plexus" version="10">
         <artifact name="plexus-10.pom">
            <sha256 value="bba9c521064b9ca132ce97cc1cc7eb4afc2dbe32bc88cb872c88e99f6162301f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.codehaus.plexus" name="plexus-utils" version="3.5.1">
         <artifact name="plexus-utils-3.5.1.jar">
            <sha256 value="86e0255d4c879c61b4833ed7f13124e8bb679df47debb127326e7db7dd49a07b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="plexus-utils-3.5.1.pom">
            <sha256 value="94ff68edeb48204d12c99189c767164d3a9f778a1372d1dce11a41462e6236f2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.connid" name="connid" version="1.3.2">
         <artifact name="connid-1.3.2.pom">
            <sha256 value="a9d00ba9710f73ca156f8897f27607e639f34f2bb7f7a220068667e20f502bc5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.connid" name="framework" version="1.3.2">
         <artifact name="framework-1.3.2.jar">
            <sha256 value="ce95f065078c1b2920ed8718a02520b8c15b90f9a7df7439b375c08de4bb7945" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="framework-1.3.2.pom">
            <sha256 value="7af1d8f42df3cc0bade80afad1a20169a768a6aed8779054c0f6b5eb7c4e5b79" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.connid" name="framework-internal" version="1.3.2">
         <artifact name="framework-internal-1.3.2.jar">
            <sha256 value="81c2aa60dc06c8f9c4e36a07c9c1b11bc93c90364118b6e5162487c34d2690f6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="framework-internal-1.3.2.pom">
            <sha256 value="da5e9de6bb7737d4b1693ade58708a7984aa3808f957febcf4e77339f2998057" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.conscrypt" name="conscrypt-openjdk-uber" version="2.5.2">
         <artifact name="conscrypt-openjdk-uber-2.5.2.jar">
            <sha256 value="eaf537d98e033d0f0451cd1b8cc74e02d7b55ec882da63c88060d806ba89c348" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="conscrypt-openjdk-uber-2.5.2.pom">
            <sha256 value="b5fd548732f932545d777890eb995222bfe866232351be908137e39c3c672d8b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.ee4j" name="project" version="1.0.6">
         <artifact name="project-1.0.6.pom">
            <sha256 value="4e7d8329d8da7dcf30779d824241be145f27108932f5a5a24eb907677bc8d72d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.ee4j" name="project" version="1.0.7">
         <artifact name="project-1.0.7.pom">
            <sha256 value="205c039a42cbae3556efbeb04a483eb3a3cf9550bd75bf84260dc8f28218f105" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.emf" name="org.eclipse.emf.common" version="2.30.0">
         <artifact name="org.eclipse.emf.common-2.30.0.jar">
            <sha256 value="4e4d516e2958a28b3dea3ab95bca91a14af3c3cebd11705d6027417a93178fde" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.emf.common-2.30.0.pom">
            <sha256 value="09ad323fb2f7bf031106cfed7270155f3405df238a7e089dd5d5233d4ab2022b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.emf" name="org.eclipse.emf.ecore" version="2.36.0">
         <artifact name="org.eclipse.emf.ecore-2.36.0.jar">
            <sha256 value="bebb33f7683098ab65997e23fc1273c58bd0d39cc9334aaa6110abe240daa3f3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.emf.ecore-2.36.0.pom">
            <sha256 value="4960daa47187c2f3ef44245759546a409a577508bfc52ae80787e0255309d3a6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.emf" name="org.eclipse.emf.ecore.change" version="2.16.0">
         <artifact name="org.eclipse.emf.ecore.change-2.16.0.jar">
            <sha256 value="77db96a3bd664e2a20d37a0f208fdea3d21a6f9a7efa11674f04582e6f789825" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.emf.ecore.change-2.16.0.pom">
            <sha256 value="8c4e8db6141199d3c6befc86b13c26723c842812a54575d5de91e0b92429aba1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.emf" name="org.eclipse.emf.ecore.xmi" version="2.37.0">
         <artifact name="org.eclipse.emf.ecore.xmi-2.37.0.jar">
            <sha256 value="86607313d60acf901bee23378c9625a591b1f346a0b9c01c657a47e235d7e805" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.emf.ecore.xmi-2.37.0.pom">
            <sha256 value="e0bd6c397b31e73302b74bac5ddc944343e117c78a46cefe879c0a62bf89b8f3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.jdt" name="org.eclipse.jdt.core.manipulation" version="1.21.0">
         <artifact name="org.eclipse.jdt.core.manipulation-1.21.0.jar">
            <sha256 value="613e0fde1facc367d70a6fd227ed554721aa636fd5776e397aee18a657a15a61" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.jdt.core.manipulation-1.21.0.pom">
            <sha256 value="a0ff661e4ccce19a85b8f4fe6fcf18074e81fd25e3f79200d21709b57f4be242" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.jdt" name="org.eclipse.jdt.launching" version="3.21.100">
         <artifact name="org.eclipse.jdt.launching-3.21.100.jar">
            <sha256 value="16c12f576b672241283b9c5580a293d57964e3345d437c848946a07dc13f79a6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.jdt.launching-3.21.100.pom">
            <sha256 value="6fa601b071b072912eaa1c4af689708e41cb212992f30a29e142b88b17a2da94" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.jdt" name="org.eclipse.jdt.launching.macosx" version="3.6.200">
         <artifact name="org.eclipse.jdt.launching.macosx-3.6.200.jar">
            <sha256 value="47b27bb7955cb4631c163dade6db1fa82aa4424b7da9d8ab745dd991f95ded0a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.jdt.launching.macosx-3.6.200.pom">
            <sha256 value="9a453e180a3ca152e38bca9c03a121517f696700aa094bad5220927f92aab412" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.jdt" name="org.eclipse.jdt.ui" version="3.32.0">
         <artifact name="org.eclipse.jdt.ui-3.32.0.jar">
            <sha256 value="12ae5dad5c01e4ff5313a6d463ec46564dc610f1de373970695b7dfed88db20a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.jdt.ui-3.32.0.pom">
            <sha256 value="7e9756ae2a1da45a610d048f778b253ce7abd646a0d7392c2745125b5f84bedf" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.jetty" name="jetty-bom" version="9.4.54.v20240208">
         <artifact name="jetty-bom-9.4.54.v20240208.pom">
            <sha256 value="d344104a6ee619da6598403c25d03aaaaaf0f54e9646fd3512458df57c9aaeb8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.jetty.toolchain" name="jetty-servlet-api" version="4.0.6">
         <artifact name="jetty-servlet-api-4.0.6.jar">
            <sha256 value="d90bf1f8a9d2ba89f4510bb51e1516dcf94ef6dc034e00f233654abdd78f2210" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jetty-servlet-api-4.0.6.pom">
            <sha256 value="28a224eef46983c7d06c9062f0b64f778992f0fbf6bdd416a3f983162fe87b57" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.jetty.toolchain" name="jetty-toolchain" version="1.7">
         <artifact name="jetty-toolchain-1.7.pom">
            <sha256 value="7c1532622470a0ccab07e06c6c58290ff1b25836acf2dc5e7a91a8d6d87a58ee" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.jgit" name="org.eclipse.jgit" version="6.10.0.202406032230-r">
         <artifact name="org.eclipse.jgit-6.10.0.202406032230-r.jar">
            <sha256 value="43f92f3adb681a5f3006b979e8d341c12a8cfd8029f287c42bcf0a80377565ae" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.jgit-6.10.0.202406032230-r.pom">
            <sha256 value="05595442beb6a206108b673aa9c6692c83e41df183177dc67113b1cc3f5281dd" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.jgit" name="org.eclipse.jgit-parent" version="6.10.0.202406032230-r">
         <artifact name="org.eclipse.jgit-parent-6.10.0.202406032230-r.pom">
            <sha256 value="f2d3539a0a7922fd7947082c1901d2090dae074986b22dabd973b4398cd1ea2e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.compare" version="3.10.0">
         <artifact name="org.eclipse.compare-3.10.0.jar">
            <sha256 value="2fc84d479f50503c5aaf80738cab39ba67d8caa184c154864b2e38c080fb4997" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.compare-3.10.0.pom">
            <sha256 value="7248c07025a6eb8d3c353b62a0addc205f8cb482b0205b5e06efa70716846ce8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.compare.core" version="3.8.400">
         <artifact name="org.eclipse.compare.core-3.8.400.jar">
            <sha256 value="ff56791d2400d9cea7f0e77caca29661426b33b329145217937eafe0bdd8ab8a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.compare.core-3.8.400.pom">
            <sha256 value="1f20a9f3d9e2a95fa1a48a0b08e084666b2c689fbb64099e47b7899a5835eed6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.commands" version="3.12.0">
         <artifact name="org.eclipse.core.commands-3.12.0.jar">
            <sha256 value="da31fa772bffd30db84486a2ddf43c5f67774eaf3931f06d3d83b969a57ea790" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.commands-3.12.0.pom">
            <sha256 value="dfe1f0ce386c42c6e945ae5b84abdce7227a5c4521547b6e82eba7a333dc9481" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.contenttype" version="3.9.300">
         <artifact name="org.eclipse.core.contenttype-3.9.300.jar">
            <sha256 value="c2c553b07ad8a69e1c8cdea48697d8d8857af140f50e4e12fecf465fb4bc2fbd" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.contenttype-3.9.300.pom">
            <sha256 value="a032c7166c4706364532a91d284a9ac63001a9881e4d4072caa7686fbddcb201" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.databinding" version="1.13.200">
         <artifact name="org.eclipse.core.databinding-1.13.200.jar">
            <sha256 value="6c340ea1ea3903860700ae3bbbf2fd7cca656d4e41c05c95b350a006acefc75a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.databinding-1.13.200.pom">
            <sha256 value="2ffe8d90caddd021db180b41e4e6b76e39d64038d87b06f6e69a50758c0a165b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.databinding.observable" version="1.13.200">
         <artifact name="org.eclipse.core.databinding.observable-1.13.200.jar">
            <sha256 value="0213bd6874d5fc8146bf428214a47e6879319669c5bacc43ebbb7309f2ab711a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.databinding.observable-1.13.200.pom">
            <sha256 value="8c1723d99d08e8ffc3a2420e9322e60784cce036f12c4419c0c3f8e7faadb1f6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.databinding.property" version="1.10.200">
         <artifact name="org.eclipse.core.databinding.property-1.10.200.jar">
            <sha256 value="de40e6775e53abaf8833b9403aa309b839e8d20690df4b76f8ffda54294beb1a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.databinding.property-1.10.200.pom">
            <sha256 value="2f4f5986629afb7f300af0aaca01c58c91d520b084d202fdfb49e3a9ec661bfa" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.expressions" version="3.9.300">
         <artifact name="org.eclipse.core.expressions-3.9.300.jar">
            <sha256 value="b85cfbc5198b0abc04eb2ec312c802acf8a9d10307bdb9fa46b086951c555827" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.expressions-3.9.300.pom">
            <sha256 value="6bb98c44f1850b142a202e392573ba2ba845b4e409d4852c9057d294d9b40470" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.filebuffers" version="3.8.300">
         <artifact name="org.eclipse.core.filebuffers-3.8.300.jar">
            <sha256 value="1e137334fefbb28d878dfda0cf5b9d51f7290415715bf34aaf988f18347df43c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.filebuffers-3.8.300.pom">
            <sha256 value="de49a4bdf0ee035a608f7903d8a17651369b29d4c511abe1090e93d9a51ef8fb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.filesystem" version="1.10.300">
         <artifact name="org.eclipse.core.filesystem-1.10.300.jar">
            <sha256 value="710aa427aa95ecf2d1569930223c33ddf84a831836874ded5c299db83d21743d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.filesystem-1.10.300.pom">
            <sha256 value="e1b78379510e0803dbe7e5c12e692525b39bc36eeede92ebb908bf0f76945f15" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.jobs" version="3.15.200">
         <artifact name="org.eclipse.core.jobs-3.15.200.jar">
            <sha256 value="ceb9e68fd2226068a0e07bda3c8a21bd6cd996552874934b8026c58547ccdbd3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.jobs-3.15.200.pom">
            <sha256 value="cf8f81a97fd6d96acf28159a78a0dca493a9a97813384f820712212b64803c2d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.resources" version="3.20.100">
         <artifact name="org.eclipse.core.resources-3.20.100.jar">
            <sha256 value="60880e30a297784a37ceb944d339852a8a6d33ff0615da043f58b458232cd163" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.resources-3.20.100.pom">
            <sha256 value="bd8480580572282742c7aa495c0ad6ebf062ce11ce5f7491f0582ab9931ec116" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.runtime" version="3.31.0">
         <artifact name="org.eclipse.core.runtime-3.31.0.jar">
            <sha256 value="f883cb596392adb49fa45b97e5a58b219c44d5f151c9134a4d05c68312a0a06f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.runtime-3.31.0.pom">
            <sha256 value="7d306abc1728a89f22736e685245bc72106421b7302473a1923d63a1ce6f478b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.core.variables" version="3.6.300">
         <artifact name="org.eclipse.core.variables-3.6.300.jar">
            <sha256 value="1ea3de6dd66cef7e73e829039ec80f337f02c0ef773f52d4deb121c09a58e8cd" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.core.variables-3.6.300.pom">
            <sha256 value="d7e55a9b29a1c7225b415cd441c71d8d91bd798eb54b60a4bb2742b0bc246959" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.debug.core" version="3.21.300">
         <artifact name="org.eclipse.debug.core-3.21.300.jar">
            <sha256 value="b935d950dc0c8a55738203395dede163d99ac240f5b0c6a3dc653e7871aa83ea" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.debug.core-3.21.300.pom">
            <sha256 value="38fd0642d52c70b00d2114dac38bfdfe90c496fa680de1d60b2a753ebd73058f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.debug.ui" version="3.18.300">
         <artifact name="org.eclipse.debug.ui-3.18.300.jar">
            <sha256 value="496f9f2b104d7699c6861094f55d512c2ab769f1e3438ccb49a1ed5dd06936d0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.debug.ui-3.18.300.pom">
            <sha256 value="2f3e252f310ea24ea303a57c0d113b999ca4539c21d52464db3437fb3fd6961f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.core.commands" version="1.1.300">
         <artifact name="org.eclipse.e4.core.commands-1.1.300.jar">
            <sha256 value="85d8e43f525f6fc13efda2b38c812d5705462f610f24e8f907f33f9a56fcf927" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.core.commands-1.1.300.pom">
            <sha256 value="8e04a7bde9f7ffe411f4e5607fc276f359770056e629f8c595565b345433037d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.core.contexts" version="1.12.500">
         <artifact name="org.eclipse.e4.core.contexts-1.12.500.jar">
            <sha256 value="3770a6e2ac5ef1c6638edc6bbc993ecaf21ac31de74b1f9b35423cef51dd1dee" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.core.contexts-1.12.500.pom">
            <sha256 value="6d994adddaeb0b8dd9272dcbf3b6e3a525cd70b6103fbb67bff1f7b33655a203" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.core.di" version="1.9.300">
         <artifact name="org.eclipse.e4.core.di-1.9.300.jar">
            <sha256 value="9fa9a45c276cb71e6c8b1e1219a4f6c2b54b21610172ee2696b95b53d413fa7e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.core.di-1.9.300.pom">
            <sha256 value="719e1e654a4afe514a6831f056cca986aa5bbdae2fb40b7c771a9fc60cfb24d3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.core.di.annotations" version="1.8.300">
         <artifact name="org.eclipse.e4.core.di.annotations-1.8.300.jar">
            <sha256 value="65b551c375e9d703314c7733566908ca0a25cc2766d3017f2370cefa912bb8d9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.core.di.annotations-1.8.300.pom">
            <sha256 value="5a3fe4a83e5c0896273c3c77bd33e637052eeaf3f4f8ffe8114c71725abef6cf" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.core.di.extensions" version="0.18.200">
         <artifact name="org.eclipse.e4.core.di.extensions-0.18.200.jar">
            <sha256 value="067b33f67346279a75bc262e0ba8347eb2185dacdbc997497b45564514ed0750" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.core.di.extensions-0.18.200.pom">
            <sha256 value="9f546ce129de13d92fba84fdf70cd70e67680eef5de556630359abf804b7f559" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.core.di.extensions.supplier" version="0.17.400">
         <artifact name="org.eclipse.e4.core.di.extensions.supplier-0.17.400.jar">
            <sha256 value="ffe749aa2516549cc7d8deb47bf09336c4e8ea3153e1eca14108cc1ed8308419" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.core.di.extensions.supplier-0.17.400.pom">
            <sha256 value="b4843d7c93d4a9ca2b09ff281ddcff8fbfe0ef8ca3741a531dfac73f92134209" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.core.services" version="2.4.300">
         <artifact name="org.eclipse.e4.core.services-2.4.300.jar">
            <sha256 value="9319177737deee94307fd4b90f9bdeddda3f82c7792f7f194c406b0ce44eed02" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.core.services-2.4.300.pom">
            <sha256 value="67b1c1cc857af1cb71e44a7c53954453cba54b1066b8d24a5193696c50b19442" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.emf.xpath" version="0.4.200">
         <artifact name="org.eclipse.e4.emf.xpath-0.4.200.jar">
            <sha256 value="17984f706c62a1ef3b0b281b01d7e360d7afda3134b3ab707a986f81412c96d3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.emf.xpath-0.4.200.pom">
            <sha256 value="b956bee9c4538efbe8367998714fa95566e553d57ef6f70b4a03e7bfc0197d29" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.bindings" version="0.14.300">
         <artifact name="org.eclipse.e4.ui.bindings-0.14.300.jar">
            <sha256 value="28a396f756b6a4922996a5d7f7317d0324fb7eb2c4930a4b67876787b4f3ac67" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.bindings-0.14.300.pom">
            <sha256 value="6321c8cbe4bea5621933fc850fbc2ff6f6080bdf8771d1fce4902a30a0d302c4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.css.core" version="0.14.300">
         <artifact name="org.eclipse.e4.ui.css.core-0.14.300.jar">
            <sha256 value="c8216335db23000a00e1b623e140aa943001f857730264f8b1828a4f3d885152" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.css.core-0.14.300.pom">
            <sha256 value="48333d25db350b98baac1bd6b15a64241f72fb1ba3131252f701d7e4d90adffa" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.css.swt" version="0.15.300">
         <artifact name="org.eclipse.e4.ui.css.swt-0.15.300.jar">
            <sha256 value="ca771d09b6844cc2b621277a10487cfd56abb781c576346becfac9b59ba94794" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.css.swt-0.15.300.pom">
            <sha256 value="abf7b9c4a4e64165edcdeba4707d0149f8dd33b47228529db5027610b754a4e6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.css.swt.theme" version="0.14.300">
         <artifact name="org.eclipse.e4.ui.css.swt.theme-0.14.300.jar">
            <sha256 value="b4f140b92a5731b3b2da846cbe6c00f881b7fb6da9e16d5918ad8ca8939709d0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.css.swt.theme-0.14.300.pom">
            <sha256 value="e00072ec7ee16ed2ea22db2568ae63801835c8811e001cb227d784dcb007c3f0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.di" version="1.5.300">
         <artifact name="org.eclipse.e4.ui.di-1.5.300.jar">
            <sha256 value="cd933ace3ff76756aae861f905690b7de20d9f7bbf1924ea6d42c8a6909a0470" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.di-1.5.300.pom">
            <sha256 value="3cdc1fdf66fc9378c209332045925012af37ecb975562d9d073252031c297364" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.dialogs" version="1.4.200">
         <artifact name="org.eclipse.e4.ui.dialogs-1.4.200.jar">
            <sha256 value="65d14b4aafabc893fb242c49d1555d23cf3694fdf206a5cde8783c92a3d92b29" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.dialogs-1.4.200.pom">
            <sha256 value="bf38392b61cf35fe946047fa6479fc2f035d1301323c14f008c960eb46d30d84" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.ide" version="3.17.200">
         <artifact name="org.eclipse.e4.ui.ide-3.17.200.jar">
            <sha256 value="0399b8ed7b9018c7505d2d33a0fb3917b26f73842f3f002fbc6a33614ea4cd48" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.ide-3.17.200.pom">
            <sha256 value="4af5d1fdd7ec994c3fec6f426c51e71336e82cfbab0d23908dd3359caae02eb3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.model.workbench" version="2.4.200">
         <artifact name="org.eclipse.e4.ui.model.workbench-2.4.200.jar">
            <sha256 value="2dc70ef59e4251b0e39427b3005e51c129b2f1cf3c2b2e93858704115fe39003" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.model.workbench-2.4.200.pom">
            <sha256 value="c85185a6b5ef8ff98e4a2e40e61fbe6861059b0ed5e8418ce688da5b7f760870" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.services" version="1.6.300">
         <artifact name="org.eclipse.e4.ui.services-1.6.300.jar">
            <sha256 value="0343826eadf7cf79289e1bc00f3c0b7352477d73325d0188ab4cbe21627356bd" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.services-1.6.300.pom">
            <sha256 value="2b4dfc53ff7f0bab8460ab31e640ea6a0bc4375d61227b0b12f6c9b75e360099" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.widgets" version="1.4.100">
         <artifact name="org.eclipse.e4.ui.widgets-1.4.100.jar">
            <sha256 value="31c53095082cc039f64096be6f9105f19789fef204741622fc8281f5a136522c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.widgets-1.4.100.pom">
            <sha256 value="edd74bc2984c6418b5b4ba75a07b35fa4c0dc31e4fd25f5e5b8d00db03970ca3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.workbench" version="1.15.300">
         <artifact name="org.eclipse.e4.ui.workbench-1.15.300.jar">
            <sha256 value="2dd1b03cc16b5fbc3a5a2ebc553b63742966c8aae3debbf54c51ac9b05f7baec" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.workbench-1.15.300.pom">
            <sha256 value="588407eb01c46ce5da546643c12e78b909b6e158209d5ec3be617f0a6b7e1639" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.workbench.addons.swt" version="1.5.300">
         <artifact name="org.eclipse.e4.ui.workbench.addons.swt-1.5.300.jar">
            <sha256 value="cc44a0dad13a863bb35616e68d1196f74f99bd9e65545af40e55af9b1a9b64fd" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.workbench.addons.swt-1.5.300.pom">
            <sha256 value="682682df3e5477d60344f7f0a1517d14efdcd38b32a9803649e2c6bc085fa11b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.workbench.renderers.swt" version="0.16.300">
         <artifact name="org.eclipse.e4.ui.workbench.renderers.swt-0.16.300.jar">
            <sha256 value="3da274a3673bc2012116f88766de33a7bea9453bc1c7a73a56d5c9a6fe5ded17" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.workbench.renderers.swt-0.16.300.pom">
            <sha256 value="d635b04aae682715f7a8d1e229d0a5566a874c5856ebe20dfbf14c54944712ae" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.workbench.swt" version="0.17.300">
         <artifact name="org.eclipse.e4.ui.workbench.swt-0.17.300.jar">
            <sha256 value="0bc467211bf0a0ff9d98bb31920497e532b35cb28e6e0e83a4a8d9361048769f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.workbench.swt-0.17.300.pom">
            <sha256 value="2e0e6226df2b2cdf4109a9052c59df9bcae18a58b15d2e2b176a17a71118256c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.e4.ui.workbench3" version="0.17.300">
         <artifact name="org.eclipse.e4.ui.workbench3-0.17.300.jar">
            <sha256 value="03c76fa29603053d8245d067f7279cf3a213fd19d1cceb903611ed96f52cf156" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.e4.ui.workbench3-0.17.300.pom">
            <sha256 value="1e8ab095e5e21cf9de8ac47c97b1047961d21ecad43774299c8df109da59747d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.app" version="1.7.0">
         <artifact name="org.eclipse.equinox.app-1.7.0.jar">
            <sha256 value="52f333392fcf4f58264c65febce3598fb1f53a55a94cdddb8b3006c9c631e29a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.app-1.7.0.pom">
            <sha256 value="0c31d5a95b40ad27f5f2dbc3f17ba4b699062c2fb6b5723f38730b5a6709e693" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.bidi" version="1.5.0">
         <artifact name="org.eclipse.equinox.bidi-1.5.0.jar">
            <sha256 value="3827300408fcb00a47da6b28bbb5f48fb44e5690eacc337eb7a6bca91626d30a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.bidi-1.5.0.pom">
            <sha256 value="3c62088d4d55e2604d12d5778185ca626812ff9bcda22653bec8da9026121ac3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.common" version="3.19.0">
         <artifact name="org.eclipse.equinox.common-3.19.0.jar">
            <sha256 value="67474862af2ff101aaa4ddd9e097bb0f650ed61bb00367e2c1d86cc266ac97e1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.common-3.19.0.pom">
            <sha256 value="d8853b1ad05825c51f18e7f6305da0cd8755af3090aa62fb6b31899987c62716" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.event" version="1.7.0">
         <artifact name="org.eclipse.equinox.event-1.7.0.jar">
            <sha256 value="f4928f135393fb7df0ae18b8a8658f1c24c821f4c6c347b7436d694879d2efe2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.event-1.7.0.pom">
            <sha256 value="cf54e475ef66138476828938c772e71bed8ed49698d638c17b8416050577b82c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.p2.artifact.repository" version="1.5.300">
         <artifact name="org.eclipse.equinox.p2.artifact.repository-1.5.300.jar">
            <sha256 value="785d9cd85a26b7b13aa2aa20f4417dfb3efc0078e95ca0544379b999080d0e55" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.p2.artifact.repository-1.5.300.pom">
            <sha256 value="cea7870d7e49d2f82a9f19695eb0da5f824a3ea49888b80909ef3eb9f9c4dc44" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.p2.core" version="2.11.0">
         <artifact name="org.eclipse.equinox.p2.core-2.11.0.jar">
            <sha256 value="0bfe574268cdc390057665318c017a835384ec14d0e3dcb56035786d2aa41ead" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.p2.core-2.11.0.pom">
            <sha256 value="cef5d7285bd5bb086075f3dea59ac96a4fcac4fc4bd6e3ab5fc74e883e76731c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.p2.engine" version="2.10.0">
         <artifact name="org.eclipse.equinox.p2.engine-2.10.0.jar">
            <sha256 value="06a5a10921204a9b3895754d4a817cd2c8a8942a550d3aa6e41c48222528a6b6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.p2.engine-2.10.0.pom">
            <sha256 value="c97827d79860a9c77a564e40644aadb25d929ad63f94951775c1852b1d263784" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.p2.jarprocessor" version="1.3.300">
         <artifact name="org.eclipse.equinox.p2.jarprocessor-1.3.300.jar">
            <sha256 value="05dff65e8149708b5463a3b79d2e506febf28055570586c9fc7b6ee24a3b4f1e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.p2.jarprocessor-1.3.300.pom">
            <sha256 value="e779b3a31a3efbbf13dc0bf75b23c4c2fb3ec0bd7d0fea4ea08f958c9ee77e4d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.p2.metadata" version="2.9.0">
         <artifact name="org.eclipse.equinox.p2.metadata-2.9.0.jar">
            <sha256 value="2c0c4eca95e8ec254ef2568c420a3c5e591d6b429f6842830c7f07b5758a2d46" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.p2.metadata-2.9.0.pom">
            <sha256 value="62dcbdac79f73d55cd6a6f634ca54543ffa175c45491c7c10255d27233e2c083" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.p2.metadata.repository" version="1.5.300">
         <artifact name="org.eclipse.equinox.p2.metadata.repository-1.5.300.jar">
            <sha256 value="92bc050d9f931adb9449206189f554437266c557f895f34869127b345053ff9d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.p2.metadata.repository-1.5.300.pom">
            <sha256 value="e752b337f970c62ff21170b7e5a3a5854d650c3548cc805770ceb854293188ee" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.p2.repository" version="2.8.100">
         <artifact name="org.eclipse.equinox.p2.repository-2.8.100.jar">
            <sha256 value="4da12ec9082ae87a00145d5148a5ace953e52ffc277bdfb9118fbbf2291abc64" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.p2.repository-2.8.100.pom">
            <sha256 value="d5f441acf878319be2d31a930240cecbb6db3273814a55b8b097a51a5ad7d552" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.preferences" version="3.11.0">
         <artifact name="org.eclipse.equinox.preferences-3.11.0.jar">
            <sha256 value="3a0b0d0013c02973ce45bf42f46a837c9dd218017cb469170253ea021b580045" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.preferences-3.11.0.pom">
            <sha256 value="c17b79ad671b04b9a065d33a3c98099fe878d9475412b66fc60cbca5b66ed3af" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.registry" version="3.12.0">
         <artifact name="org.eclipse.equinox.registry-3.12.0.jar">
            <sha256 value="9c8cb9ac4bf6088a9c54f69ffb44a36cb87c602fb82b474ee63c9b6be9faff30" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.registry-3.12.0.pom">
            <sha256 value="099ba72261fca67270ca829693dca7c45ade92afa12c6dfc903ee8e68e1ab2bf" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.security" version="1.4.200">
         <artifact name="org.eclipse.equinox.security-1.4.200.jar">
            <sha256 value="6792cb28e251759ed3d1f72e29f4d1d798ea6eb3d72d6ba3b75c36f1b850f87c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.security-1.4.200.pom">
            <sha256 value="2de4e10dba938ccd2dea3ca2beb480f6270e38ac48e690bb2eea980664163e8c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.equinox.supplement" version="1.10.800">
         <artifact name="org.eclipse.equinox.supplement-1.10.800.jar">
            <sha256 value="66544b394b7ae0d4e1a6177cc806a1c5753ab7b11747aa43804adb643f1e50f3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.equinox.supplement-1.10.800.pom">
            <sha256 value="a73679d62c9e288a30c61ae28cddca07f7c7c58ecb7bd46aa8a6f64e22ab56fb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.help" version="3.10.300">
         <artifact name="org.eclipse.help-3.10.300.jar">
            <sha256 value="531eed215f511c23eb3c47f0ca63595b358deebb4e552bc37b218ab4553af669" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.help-3.10.300.pom">
            <sha256 value="ca9b4610de2816cb8fc60337e738a7e701d6bb24145ef94c3b6c963a86431c9a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.jface" version="3.33.0">
         <artifact name="org.eclipse.jface-3.33.0.jar">
            <sha256 value="ed854ce69edb931e954ba5a155fe0e8dd61ee5f65b99f33a1c767210e1ee2fb2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.jface-3.33.0.pom">
            <sha256 value="6112debbe59dd8732c432ed3e28c9511bfd8776e10790a09aa3d6ee95edcc79c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.jface.databinding" version="1.15.200">
         <artifact name="org.eclipse.jface.databinding-1.15.200.jar">
            <sha256 value="5039b3ffbf4380a19d317f3391e76957ca2d311797e4d8af2218405a57033a36" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.jface.databinding-1.15.200.pom">
            <sha256 value="b5ffaad3c230995d734f8f537d8913a879e87b1928945b34e93fa800929b0d44" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.jface.text" version="3.25.0">
         <artifact name="org.eclipse.jface.text-3.25.0.jar">
            <sha256 value="160d76687fcdd3a62ad6e70327a321d80210c6315b36c9e90558f2795b723f6a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.jface.text-3.25.0.pom">
            <sha256 value="18850c276d144d83a7858aea2349efaeec9419aa65f557da3de0d086e956190d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ltk.core.refactoring" version="3.14.300">
         <artifact name="org.eclipse.ltk.core.refactoring-3.14.300.jar">
            <sha256 value="ea71be55f890a01f00860866bd2d51b47b9af061ccdb75028c70a6f0f2a09e17" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ltk.core.refactoring-3.14.300.pom">
            <sha256 value="e735ddff19db4c2c99ed63e41b0679d2f765b5f9e4c313a7894771af0e6a19ef" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ltk.ui.refactoring" version="3.13.300">
         <artifact name="org.eclipse.ltk.ui.refactoring-3.13.300.jar">
            <sha256 value="0b0accdd7cfeb359ccabd21c346a7dc9bf33aeb91e2539ad1bf77977bc1d039c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ltk.ui.refactoring-3.13.300.pom">
            <sha256 value="cd4daf8add4255137fb1f6dbd3c6ccbea98cf9b6ec6f795e298db729d6430462" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.osgi" version="3.19.0">
         <artifact name="org.eclipse.osgi-3.19.0.jar">
            <sha256 value="54fdb98ced9062689b0459d67a202137867e8647c486b7f47ad64ea3160efa2e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.osgi-3.19.0.pom">
            <sha256 value="73b2d741720f1435f4a507a71d4f87806aa23372206790a95c0f751b30f581a4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.osgi" version="3.23.0">
         <artifact name="org.eclipse.osgi-3.23.0.jar">
            <sha256 value="1ac113541a19f0c72c0421fb24058defca7e3c6f282e5ee73f14d2768a9ae653" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.osgi-3.23.0.pom">
            <sha256 value="b5bb979dd6b126b8c9d5c0d57b7690ca1674073d2841093b96c5b1cf44071329" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.search" version="3.16.100">
         <artifact name="org.eclipse.search-3.16.100.jar">
            <sha256 value="c8a4876c2c31439ed0a5b57299ad54f74e939a84ad873b8f3c9a8754495ad3ff" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.search-3.16.100.pom">
            <sha256 value="8f66a109b0db3246c24325ef11039a6cc3e661252ba60906a2cb4b88a389f0a0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.search.core" version="3.16.100">
         <artifact name="org.eclipse.search.core-3.16.100.jar">
            <sha256 value="c90afbfdb86b15ebb44efe4ea6dd4a515a8d07909e3d316b3f055589010abe86" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.search.core-3.16.100.pom">
            <sha256 value="832f50d975e20cd90bdcec295f5e93f75a6dff67f62ffc3c1b8c26a1d42edc04" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.swt" version="3.125.0">
         <artifact name="org.eclipse.swt-3.125.0.jar">
            <sha256 value="40a6c73aab5e11f23f49ccc85472f072b62d99e771baa02e9b3d0e83a82e3140" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.swt-3.125.0.pom">
            <sha256 value="5676afa3f22cce3f91193c39f6cb01dd9d28437ba205341f9106a29b904ee1d5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.swt.cocoa.macosx.aarch64" version="3.125.0">
         <artifact name="org.eclipse.swt.cocoa.macosx.aarch64-3.125.0.jar">
            <sha256 value="899d16d7a8a79b72f0490726bcb52b91d2b2620a0719e91a14fe13cc47c23980" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.swt.cocoa.macosx.aarch64-3.125.0.pom">
            <sha256 value="f085f847dc00bd309b4e6113f2dc0d13b08119758acbfcb6c8310c862a3f35dc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.swt.cocoa.macosx.x86_64" version="3.125.0">
         <artifact name="org.eclipse.swt.cocoa.macosx.x86_64-3.125.0.jar">
            <sha256 value="b0fb014df21a8baaec9663ce1673dc8a40db7dfd44eb5fdd1affe9cb4d64a8a2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.swt.cocoa.macosx.x86_64-3.125.0.pom">
            <sha256 value="209b6a1e1c2faf8d389c738b34b0ffc6b22796f99f7f36bfdfb300ae7e6b6913" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.swt.gtk.linux.aarch64" version="3.125.0">
         <artifact name="org.eclipse.swt.gtk.linux.aarch64-3.125.0.jar">
            <sha256 value="30bf3bc416efb7eb20e86b5e454ce6f2424eaf7d8663a7f54ec1638bc29b2fde" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.swt.gtk.linux.aarch64-3.125.0.pom">
            <sha256 value="0e6eaf87af9ede0e92f56f156dcc5ac842c3e874b0f80b3b0cf66b2100d32581" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.swt.gtk.linux.ppc64le" version="3.125.0">
         <artifact name="org.eclipse.swt.gtk.linux.ppc64le-3.125.0.jar">
            <sha256 value="96d57df853eb1ca614aae0ba3a236b94ab49353aa50073656a176609449debfb" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.swt.gtk.linux.ppc64le-3.125.0.pom">
            <sha256 value="b52ae8f99f4b2ae43d83b09b7f930cb55245b554b7e0324654ec7c9c674362f0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.swt.gtk.linux.x86_64" version="3.125.0">
         <artifact name="org.eclipse.swt.gtk.linux.x86_64-3.125.0.jar">
            <sha256 value="2c1f775ae9affea2e6377b70ce2e723699c8d002dda0e0c3ab9374743527852e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.swt.gtk.linux.x86_64-3.125.0.pom">
            <sha256 value="9aa34fa22e146126bd217588a6959b4db8c0b347d7144e814dea36a9ea2ee570" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.swt.win32.win32.x86_64" version="3.125.0">
         <artifact name="org.eclipse.swt.win32.win32.x86_64-3.125.0.jar">
            <sha256 value="743e7522df0aef689c8af45a4ebd280786236d2773db457f4a5f2b0170951936" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.swt.win32.win32.x86_64-3.125.0.pom">
            <sha256 value="66e367dd36e55e1ca484926bb5c456394e6605c62155acdc5ba66e35d463335b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.team.core" version="3.10.300">
         <artifact name="org.eclipse.team.core-3.10.300.jar">
            <sha256 value="6dfe36ca0ae30cbcb11977b4d652b9ab44efdb134bb9b8dc7ea39178bae1e72a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.team.core-3.10.300.pom">
            <sha256 value="9940069bc0b9e7a1d3c03301988bcaa2b79af5001b290ba206cb41507018637c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.team.ui" version="3.10.300">
         <artifact name="org.eclipse.team.ui-3.10.300.jar">
            <sha256 value="72235cf073e0d6bc872438f4d06c7fa105ca0b078af169daa4556859813c0e9b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.team.ui-3.10.300.pom">
            <sha256 value="5da13308bb5a3bc95941df7b28590266201b4b6a4557ce156cca3ab223d5a70f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.text" version="3.14.0">
         <artifact name="org.eclipse.text-3.14.0.jar">
            <sha256 value="b2fed1a6a45b17a185a19c07d082a1c69718139e101b0afbf0db0e756322d9fc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.text-3.14.0.pom">
            <sha256 value="a2111883bffdcf6fe579554f0e78e9fdcfb782d06e1c9528d58d230d83a40e0b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ui" version="3.205.100">
         <artifact name="org.eclipse.ui-3.205.100.jar">
            <sha256 value="e20e347b26986c34bc397fbc0a2928cd48ab4cb111ed66c08a2a052d63a51b02" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ui-3.205.100.pom">
            <sha256 value="1702184c4eebff0b48bd240d102c7d425023cefa3e9c3f8369b5e70b8c240212" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ui.console" version="3.14.0">
         <artifact name="org.eclipse.ui.console-3.14.0.jar">
            <sha256 value="81c54f2aa8c095e11fa9f6bdcf37ea31738aa4125dc07c652e322deabefcbb6f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ui.console-3.14.0.pom">
            <sha256 value="562dfbd274627015673e779990426248be76a7d42870aa422829e841d34970ec" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ui.editors" version="3.17.200">
         <artifact name="org.eclipse.ui.editors-3.17.200.jar">
            <sha256 value="a36e96df92ae04ff42430ce896c2ca897ba5d534200bdf649844dcb9c90af0ff" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ui.editors-3.17.200.pom">
            <sha256 value="79d815547b5733fac915305e2cb390d52fc15250b0e06cb8b7ea286d176a2245" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ui.forms" version="3.13.200">
         <artifact name="org.eclipse.ui.forms-3.13.200.jar">
            <sha256 value="de5b84e03fbd0b3bdf6e9374e83ff6be89e365316cb2c96fd1f929da0dc6c49c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ui.forms-3.13.200.pom">
            <sha256 value="09038199e2e38e7101130e440e77ea5e89e0b4f87a9a04f29acc445f4e647d88" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ui.ide" version="3.22.100">
         <artifact name="org.eclipse.ui.ide-3.22.100.jar">
            <sha256 value="96c5993ee3c027a6c47f2bcc535c8f45d537cd0089ea0baeb31729823257ab9d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ui.ide-3.22.100.pom">
            <sha256 value="807af2487468cebcfa3483426e018d3db095da93a6bedd562afedf0fb414d067" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ui.navigator" version="3.12.300">
         <artifact name="org.eclipse.ui.navigator-3.12.300.jar">
            <sha256 value="89839c384d4f503b01112582ba3ae4c77f60b209909826287bb869cfa54e22ce" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ui.navigator-3.12.300.pom">
            <sha256 value="e3b58c2cad00d934f97ac64009d2763db2d8d45b62f8a7dbdacd239adfea6df8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ui.navigator.resources" version="3.9.200">
         <artifact name="org.eclipse.ui.navigator.resources-3.9.200.jar">
            <sha256 value="35f4c9ca6ad781cde4775137e956e84b27cb6c4cba99bc3918b0fe9ae83b8d1b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ui.navigator.resources-3.9.200.pom">
            <sha256 value="31333836f2a7411123690ec91d3e983a37a458c700f00256557c3b2e610af57b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ui.views" version="3.12.200">
         <artifact name="org.eclipse.ui.views-3.12.200.jar">
            <sha256 value="7ba31422cb2a4e371717c31b5f6df4457c06f37203cc10340e9f3479cb67dba6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ui.views-3.12.200.pom">
            <sha256 value="d415b4c70faf50790ebf8d28970b6a74ad00d393cf1286240b6aefc9e446609c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ui.views.properties.tabbed" version="3.10.200">
         <artifact name="org.eclipse.ui.views.properties.tabbed-3.10.200.jar">
            <sha256 value="f7c66b0f3190189c8548cd32dc1376dace19002c96125e9dcc53bb50885c8acc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ui.views.properties.tabbed-3.10.200.pom">
            <sha256 value="f44a134f82c7ea860c96f4701f7e93167eb3f4c735630d98fddb7d55cb420b13" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ui.workbench" version="3.131.100">
         <artifact name="org.eclipse.ui.workbench-3.131.100.jar">
            <sha256 value="60cf66dc6d70a4c937317610cce7d696667ab5aeedefcdb707252d8d60695cc0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ui.workbench-3.131.100.pom">
            <sha256 value="a8ee5e7d1d2cda201046eca1a388af7a6dcf1c931a48b5f93f5e4f90226f778e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.ui.workbench.texteditor" version="3.17.300">
         <artifact name="org.eclipse.ui.workbench.texteditor-3.17.300.jar">
            <sha256 value="cf5e5117d204c2633c7864c2b124e9bc43a45baa0c021970eaf243c4c0eef990" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.ui.workbench.texteditor-3.17.300.pom">
            <sha256 value="ae4f2bbd1a2b207aa006db1847b77b4ff24d85aa317ad5191d2e39b7aa7f5fcb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.eclipse.platform" name="org.eclipse.urischeme" version="1.3.200">
         <artifact name="org.eclipse.urischeme-1.3.200.jar">
            <sha256 value="67a637da49f367c30d0cca823338d44e55a3254f19729cd88fe6a02b662f9fd3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.eclipse.urischeme-1.3.200.pom">
            <sha256 value="ad6e77d7826dbfd0f06d4cdff84930ad0fd35f3e590103b7b53f8794c64f37e0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.fusesource" name="fusesource-pom" version="1.12">
         <artifact name="fusesource-pom-1.12.pom">
            <sha256 value="c40d960daadcef7b01c1b1c6657afbac4fffb5e53168f8fcb0b28b84e6fdcca1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.fusesource.jansi" name="jansi" version="2.4.1">
         <artifact name="jansi-2.4.1.jar">
            <sha256 value="2e5e775a9dc58ffa6bbd6aa6f099d62f8b62dcdeb4c3c3bbbe5cf2301bc2dcc1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jansi-2.4.1.pom">
            <sha256 value="3f98d979a4d3559f8779fbb004b34ae7516afadf510e11df7cc3cd073eb1bb3b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.glassfish" name="jakarta.el" version="3.0.4">
         <artifact name="jakarta.el-3.0.4.jar">
            <sha256 value="3b8d4311b47fb47d168ad4338b6649a7cc21d5066b9765bd28ebca93148064be" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jakarta.el-3.0.4.pom">
            <sha256 value="261441ea3662f28053536cfd71b8f74822b1f83a9eb016f54a53ff004cfdf450" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.graalvm.buildtools" name="graalvm-reachability-metadata" version="0.10.6">
         <artifact name="graalvm-reachability-metadata-0.10.6.jar">
            <sha256 value="77cbcad634f9c9e1b58fcb48a2f6a08b8730fc06015df383ccac2fa5db394f7f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="graalvm-reachability-metadata-0.10.6.module">
            <sha256 value="5b2d610c8daace090755abbc4c7623eeddaa5f705b23c898a65de0d1f1b3d262" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.graalvm.buildtools" name="junit-platform-native" version="0.10.6">
         <artifact name="junit-platform-native-0.10.6.jar">
            <sha256 value="369412d22c4f71cc07748f783e02f744c407cdc9186db3788ffcbdbc39e0f46b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-platform-native-0.10.6.module">
            <sha256 value="0fda7bd165414c2cdd19417e1caaeeb1589b9405653052633a3195145af1ae32" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.graalvm.buildtools" name="native-gradle-plugin" version="0.10.6">
         <artifact name="native-gradle-plugin-0.10.6.jar">
            <sha256 value="003bf549df93cc3ab977b567ac6de06afdac091b4db09e9d9f6ac8ece5766390" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="native-gradle-plugin-0.10.6.module">
            <sha256 value="482d39995d7a95ef5cea743764fc9b5f90d96de52d52e48c2edb9bb3e799f33a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.graalvm.buildtools" name="utils" version="0.10.6">
         <artifact name="utils-0.10.6.jar">
            <sha256 value="439eb92808a742611e724a691c21fc3a9f22f2a9cdb784725e3d327739304ac4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="utils-0.10.6.module">
            <sha256 value="0979a9f3c7c9f3ba2f638ad6d7fec9bc0dbf2ec0ac3e90e6aa11fab6796a9982" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.graalvm.buildtools.native" name="org.graalvm.buildtools.native.gradle.plugin" version="0.10.6">
         <artifact name="org.graalvm.buildtools.native.gradle.plugin-0.10.6.pom">
            <sha256 value="3f308abae907154e66bf4b59ab02509e6ed05fbd06727242bd24068aaa28445c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hamcrest" name="hamcrest" version="2.1">
         <artifact name="hamcrest-2.1.jar">
            <sha256 value="ba93b2e3a562322ba432f0a1b53addcc55cb188253319a020ed77f824e692050" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="hamcrest-2.1.pom">
            <sha256 value="787fddc7ac064b5a22406380e20d560769f0a089bbecff228e19edc96cf6c927" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hamcrest" name="hamcrest-core" version="1.3">
         <artifact name="hamcrest-core-1.3.jar">
            <sha256 value="66fdef91e9739348df7a096aa384a5685f4e875584cce89386a7a47251c4d8e9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="hamcrest-core-1.3.pom">
            <sha256 value="fde386a7905173a1b103de6ab820727584b50d0e32282e2797787c20a64ffa93" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hamcrest" name="hamcrest-parent" version="1.3">
         <artifact name="hamcrest-parent-1.3.pom">
            <sha256 value="6d535f94efb663bdb682c9f27a50335394688009642ba7a9677504bc1be4129b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hibernate.validator" name="hibernate-validator" version="8.0.2.Final">
         <artifact name="hibernate-validator-8.0.2.Final.jar">
            <sha256 value="2f2224a5a19bdcfa73540e9ff5c971b6c425ad80415876f305259fe873a15b2f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="hibernate-validator-8.0.2.Final.pom">
            <sha256 value="1a8aebf549008274cb7fcd608c48922f293f2e12b9a9017c27edf8d821cc3aba" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hibernate.validator" name="hibernate-validator-parent" version="8.0.2.Final">
         <artifact name="hibernate-validator-parent-8.0.2.Final.pom">
            <sha256 value="e08aed198642584fe427117f1183f5304071accc91cc91631ff6ada2478d2a86" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hyperledger.besu" name="arithmetic" version="1.3.1">
         <artifact name="arithmetic-1.3.1.jar">
            <sha256 value="f9080e1250042b8bf91fce594412a1e024e5a31b8aed13693cc674a7449b79f0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="arithmetic-1.3.1.module">
            <sha256 value="36d4e963ffa8de7ff54c96e9c62e07cb0cb8ef8719e7ea2c4f0c509e8d28bee6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hyperledger.besu" name="besu-errorprone-checks" version="1.0.0">
         <artifact name="besu-errorprone-checks-1.0.0.jar">
            <sha256 value="91acbbe3f8de77bf66b4aa568a704cba31e216cd9d6d0d100f44d66d9b368172" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="besu-errorprone-checks-1.0.0.module">
            <sha256 value="c273525c9f23a0bd5b9cf6830b4bebd9d81e355b7f2ed3a22f23f76c2a2313d5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hyperledger.besu" name="besu-native-common" version="1.3.1">
         <artifact name="besu-native-common-1.3.1.jar">
            <sha256 value="14f3a45222b1acdc6e92c25bfdc0757277879dac57a6211c4cb9460ae38ab2fc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="besu-native-common-1.3.1.module">
            <sha256 value="44820d76b4d02f62749226f98fb1d5050d0b26c0221b45059d37e74837b8c0cb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hyperledger.besu" name="blake2bf" version="1.3.1">
         <artifact name="blake2bf-1.3.1.jar">
            <sha256 value="cc816c3f42e6a62c5726d452dd9bd413986f64da2e3919556b722374bdc3e3c1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="blake2bf-1.3.1.module">
            <sha256 value="397a186ad415910458700bbe26f527beeee66539c691444ac5bf3568825aa29a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hyperledger.besu" name="gnark" version="1.3.1">
         <artifact name="gnark-1.3.1.jar">
            <sha256 value="2b88f57076eb520337167d03532d93166aec03d0d4307ed430e69e48d4187e80" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="gnark-1.3.1.module">
            <sha256 value="c379ad1d02f03bfa5b2fcc4b344d80c15eaee5e4ed6dfb5cd1068cffc41f87d6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hyperledger.besu" name="ipa-multipoint" version="1.3.1">
         <artifact name="ipa-multipoint-1.3.1.jar">
            <sha256 value="27183cf4dcd2857407cff5996cdcf8bd6e93e40f9492c4e630ead03061e38b8d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="ipa-multipoint-1.3.1.module">
            <sha256 value="05e03345dbfa52c89b6d385e235fbcca731deba22213fe6cf568099a0666b7b5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hyperledger.besu" name="secp256k1" version="1.3.1">
         <artifact name="secp256k1-1.3.1.jar">
            <sha256 value="e88c6ade2ee1fe43c1656652d24e4d6e243c21d6d4dd5134bad263ce64cc4641" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="secp256k1-1.3.1.module">
            <sha256 value="1602ae1fb14c69002e83fc9565a4b06d30c063e18b5b3e89722fbbfcdd3e51b1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.hyperledger.besu" name="secp256r1" version="1.3.1">
         <artifact name="secp256r1-1.3.1.jar">
            <sha256 value="0379db2630e65ff2845e57bd76b4887e21e49dbf3226882ac503204a5b7db7df" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="secp256r1-1.3.1.module">
            <sha256 value="65524cb4e0e5d66d618a8387de70414544de68b48cd1b2331c5e6038e4852843" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.immutables" name="bom" version="2.10.1">
         <artifact name="bom-2.10.1.pom">
            <sha256 value="27cc11e837cf14428e32a996bfa28808999ac260d764dd96e95e9db248197c41" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.immutables" name="immutables" version="2.10.1">
         <artifact name="immutables-2.10.1.pom">
            <sha256 value="d970c0d27dbe72a29c97ef0cc67f0fd5d4915314071ee8ff6449f112b2038469" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.immutables" name="value" version="2.10.1">
         <artifact name="value-2.10.1.jar">
            <sha256 value="ee8d4eb211cb0625adac485b4f874ffff4f6e678008ea50263705212a574265c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="value-2.10.1.pom">
            <sha256 value="596cb752cbec7f74c3d442406759707ca3de33fd29e4b1e362cbe4cc6bd30d6c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.immutables" name="value-annotations" version="2.10.1">
         <artifact name="value-annotations-2.10.1.jar">
            <sha256 value="9ef9629d2b710d9d705aa154457e1ba33b8c12118129b7c400bf65d923b46f26" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="value-annotations-2.10.1.pom">
            <sha256 value="dd021960067ab4823d1807ef10bebac5f38c2b89b49f8535e488ff6b50a65cf8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jacoco" name="org.jacoco.agent" version="0.8.11">
         <artifact name="org.jacoco.agent-0.8.11.jar">
            <sha256 value="d3ed85dea78a9ed55846a7738e3a0ca15c702c661ee4bc8cbfe02a8b9f4a99c0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.jacoco.agent-0.8.11.pom">
            <sha256 value="16e05e9f49621b87c53e69350140f3c46d42d966c67a933bdf4b063a2b1c8fc5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jacoco" name="org.jacoco.agent" version="0.8.12">
         <artifact name="org.jacoco.agent-0.8.12.jar">
            <sha256 value="ab29507b750d325bbaf7ea094860fff26d27170038d8ee5f00c3074489f14637" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.jacoco.agent-0.8.12.pom">
            <sha256 value="0f9da994abd9827f957fc1ba7c5bad3fe918f62601c1d743f216b0615efe480e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jacoco" name="org.jacoco.ant" version="0.8.11">
         <artifact name="org.jacoco.ant-0.8.11.jar">
            <sha256 value="81d7eb8890d9be30a939612c295603541063529cdd03a53265aba74474b70b7c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.jacoco.ant-0.8.11.pom">
            <sha256 value="7ed103d959d0cee7babfb1307fa6e451b1696ffd3527061553b550de55201d85" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jacoco" name="org.jacoco.build" version="0.8.11">
         <artifact name="org.jacoco.build-0.8.11.pom">
            <sha256 value="5b84b15cf2eef3e59eb91bc22784833100b09df9911e3319030c3bc648bd8b0b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jacoco" name="org.jacoco.build" version="0.8.12">
         <artifact name="org.jacoco.build-0.8.12.pom">
            <sha256 value="250dcc9a1003e82095933f64869237752f36e70cb8a63f2cdf99b0c9613c09b9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jacoco" name="org.jacoco.core" version="0.8.11">
         <artifact name="org.jacoco.core-0.8.11.jar">
            <sha256 value="fcd188c688473fc8dcc0c6caaf355e7b389502243527c33b9597a3ec28791f47" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.jacoco.core-0.8.11.pom">
            <sha256 value="bb6135f10a36349cb84a5600fd8cf73fc1296a135b2f14adcd83de8cf24cabb1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jacoco" name="org.jacoco.core" version="0.8.12">
         <artifact name="org.jacoco.core-0.8.12.jar">
            <sha256 value="fca26db37c0c5fbd5dc4985237eb82866df9799d5082af899475a73f91f5b035" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.jacoco.core-0.8.12.pom">
            <sha256 value="aa0b2773786886d51d188d7ed1a43f676efa21278520445bfef0ccb5d8cbe717" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jacoco" name="org.jacoco.report" version="0.8.11">
         <artifact name="org.jacoco.report-0.8.11.jar">
            <sha256 value="8393295ae24680ed10cad8333907040f928b871332491581ca5bc784e2cb4fbe" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.jacoco.report-0.8.11.pom">
            <sha256 value="8e3b734779d5e3fd683ec015413d52f961984c50cb9045fda2b23bff5eb42381" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.java-websocket" name="Java-WebSocket" version="1.5.6">
         <artifact name="Java-WebSocket-1.5.6.jar">
            <sha256 value="ba2c5b646e115c6a9aa923139a154cbcdbf136b2b5c82bf423b1433639e0d83b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="Java-WebSocket-1.5.6.pom">
            <sha256 value="e4dc76ef840014f3739e100813426ccc045b0a17e51c5038e69fdbe1f4ced434" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jboss" name="jboss-parent" version="39">
         <artifact name="jboss-parent-39.pom">
            <sha256 value="04dff075a00094b63061af407d2816d9cde66795acae376a054bdfe8ba31e664" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jboss.arquillian" name="arquillian-bom" version="1.7.0.Alpha10">
         <artifact name="arquillian-bom-1.7.0.Alpha10.pom">
            <sha256 value="0bf2ce391957e3ae82305a4ccdcdec047cc3127af94eae8073073bd819ec1e1d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jboss.logging" name="jboss-logging" version="3.4.3.Final">
         <artifact name="jboss-logging-3.4.3.Final.jar">
            <sha256 value="0b324cca4d550060e51e70cc0045a6cce62f264278ec1f5082aafeb670fcac49" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jboss-logging-3.4.3.Final.pom">
            <sha256 value="f7639433422e8bf1f397db7575f2ab954cc20f7d1581b33f6d978db30943afec" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jboss.shrinkwrap" name="shrinkwrap-bom" version="1.2.6">
         <artifact name="shrinkwrap-bom-1.2.6.pom">
            <sha256 value="1c0ac801aa3125bde5e8816ba878a3243259c396d2f38e676e2852a9cc550036" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jboss.shrinkwrap.descriptors" name="shrinkwrap-descriptors-bom" version="2.0.0">
         <artifact name="shrinkwrap-descriptors-bom-2.0.0.pom">
            <sha256 value="878eb071ad1f4b0c81568536b2a3a4f7de85a814afc5b196d9a99b5e3c0a6583" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jboss.shrinkwrap.resolver" name="shrinkwrap-resolver-bom" version="3.1.4">
         <artifact name="shrinkwrap-resolver-bom-3.1.4.pom">
            <sha256 value="53d45df757388b693a37493b74e0d7a938c5734c15f82629d40a29dea9ceb74f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains" name="annotations" version="13.0">
         <artifact name="annotations-13.0.jar">
            <sha256 value="ace2a10dc8e2d5fd34925ecac03e4988b2c0f851650c94b8cef49ba1bd111478" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="annotations-13.0.pom">
            <sha256 value="965aeb2bedff369819bdde1bf7a0b3b89b8247dd69c88b86375d76163bb8c397" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-build-tools-api" version="2.1.0">
         <artifact name="kotlin-build-tools-api-2.1.0.jar">
            <sha256 value="f145b814f9c01328c2c369a6b8fc77434cac02a7d68f0e05d0dd4cada78bbac7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-build-tools-api-2.1.0.pom">
            <sha256 value="e3cfc8da8ddf3bbd3f0ca07183a51feb4893e8bcb8fee801f4b8ce05ef9443a9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-gradle-plugin-annotations" version="2.1.0">
         <artifact name="kotlin-gradle-plugin-annotations-2.1.0.jar">
            <sha256 value="d53e97f0e13a37d8fa98fb44cd67e45d3bcf8b4278fdbe1acd1ddbccc740f74b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-gradle-plugin-annotations-2.1.0.pom">
            <sha256 value="6c1ca8596963f1cd12951aee50e34c47b3c0a58893d20f138240f5786c68e4bf" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-gradle-plugin-api" version="2.1.0">
         <artifact name="kotlin-gradle-plugin-api-2.1.0-gradle85.jar">
            <sha256 value="bf03d64ecffcb66d73fea0991768dd75cbeb1192d47c7a48b61ba7f7ca258858" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-gradle-plugin-api-2.1.0.module">
            <sha256 value="9ad196cf9a5c7d8840ad974d697a3af94990ecf16df681e755a90bf42a3ea410" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-gradle-plugins-bom" version="2.1.0">
         <artifact name="kotlin-gradle-plugins-bom-2.1.0.module">
            <sha256 value="a81bd9adbd3ba75a05a6c3a3ed846e0c92a5a7ac4ce8363cf604b4f2c8a7b75c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-gradle-plugins-bom-2.1.0.pom">
            <sha256 value="9216b199d987cab680450aac715caa57571f8a67800e36e37026f8f453c1feb1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-native-utils" version="2.1.0">
         <artifact name="kotlin-native-utils-2.1.0.jar">
            <sha256 value="c4396feba6d0a24ab717426b36418e945b7cd0f50d77c866524f26676f968439" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-native-utils-2.1.0.pom">
            <sha256 value="e8d410f736cfaca2fb277b98e4a2796f0e544e1e096a004385c7befce531ee01" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-reflect" version="1.6.10">
         <artifact name="kotlin-reflect-1.6.10.jar">
            <sha256 value="3277ac102ae17aad10a55abec75ff5696c8d109790396434b496e75087854203" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-reflect-1.6.10.pom">
            <sha256 value="57905524274a00ae028aaccc27283f6bc5925a934a046c1cc5d06c8ee4d6d5a9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-reflect" version="1.9.22">
         <artifact name="kotlin-reflect-1.9.22.jar">
            <sha256 value="77f311ca1384811d519fda389fcb1268bdaa058d605046e0ede76c03b0df3e97" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-reflect-1.9.22.pom">
            <sha256 value="c712e358df7b9318b68f5463971b088673837fc0ca4285d1c382c8102edd6b5f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-serialization" version="2.1.0">
         <artifact name="kotlin-serialization-2.1.0-gradle85.jar">
            <sha256 value="0f0e58f1cfce6bef09c2894a74821186858d63fb0e448c3a2444793d97233bbd" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-serialization-2.1.0.module">
            <sha256 value="a7a3cb25c0ade4ddf5d665d78994e6933a05e4a65ecb2cc3437abd695cec832e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib" version="1.8.21">
         <artifact name="kotlin-stdlib-1.8.21.pom">
            <sha256 value="fe0cd9e32193e453333fd2b1f577e662fb5abc6907102bb967817bc131280fd7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib" version="1.9.10">
         <artifact name="kotlin-stdlib-1.9.10.jar">
            <sha256 value="55e989c512b80907799f854309f3bc7782c5b3d13932442d0379d5c472711504" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-1.9.10.pom">
            <sha256 value="7e29fbf73fdf71e0679d3dee7e680fd573464fa01644a4f58ab819d2c088d3d2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib" version="2.0.21">
         <artifact name="kotlin-stdlib-2.0.21.jar">
            <sha256 value="f31cc53f105a7e48c093683bbd5437561d1233920513774b470805641bedbc09" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-2.0.21.module">
            <sha256 value="81fd6d181012487ee3246eff4e2bacb64b58c46e5b5aa72971a4ddf1bd1541ed" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib" version="2.1.0">
         <artifact name="kotlin-stdlib-2.1.0.jar">
            <sha256 value="d6f91b7b0f306cca299fec74fb7c34e4874d6f5ec5b925a0b4de21901e119c3f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-2.1.0.module">
            <sha256 value="dcfbc8e8bf32cd67a7efadd91d31152bce9870911d6ec50878f4fdb6e03e70e2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-2.1.0.pom">
            <sha256 value="134e48c17796c1c1027ecbe6c9f1c75c792f535987ab89e1e1dda43f8c366f5e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-common" version="1.9.10">
         <artifact name="kotlin-stdlib-common-1.9.10.jar">
            <sha256 value="cde3341ba18a2ba262b0b7cf6c55b20c90e8d434e42c9a13e6a3f770db965a88" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-common-1.9.10.pom">
            <sha256 value="7d4b70547910676b3bdfc8925a88f3b6bfb24582c9784542805544ceef490a92" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-common" version="1.9.22">
         <artifact name="kotlin-stdlib-common-1.9.22.module">
            <sha256 value="f93c9e9abf8d52d8e8fd8e851aa802ecec55132161c4aeee7d3cd924bf794246" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-common" version="2.1.0">
         <artifact name="kotlin-stdlib-common-2.1.0.module">
            <sha256 value="2b9a5ae785f8513a93f8cec3f6e5e07f8b1766f8497b3a487f3441a251d67563" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-common-2.1.0.pom">
            <sha256 value="4a9da7a9e5295bd542d5863cae035f7af9ca101f225c4a0891c3d2df8dc2a80d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-jdk7" version="1.8.0">
         <artifact name="kotlin-stdlib-jdk7-1.8.0.jar">
            <sha256 value="4c889d1d9803f5f2eb6c1592a6b7e62369ac7660c9eee15aba16fec059163666" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-jdk7-1.8.0.pom">
            <sha256 value="dfa9644a6ae5b898ee475bb1f57e830ba1f770aee6c9c15f8112aa381180184a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-jdk7" version="1.8.21">
         <artifact name="kotlin-stdlib-jdk7-1.8.21.pom">
            <sha256 value="9bb107d5d5e3930bc5977f007a43cd20b7d24d91b1c1e528ea6ee0f248f14d36" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-jdk7" version="1.9.10">
         <artifact name="kotlin-stdlib-jdk7-1.9.10.jar">
            <sha256 value="ac6361bf9ad1ed382c2103d9712c47cdec166232b4903ed596e8876b0681c9b7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-jdk7-1.9.10.pom">
            <sha256 value="c7fa67c7961320b89d85a3ca59a2e18c2c65850845595dcae4b46af6945edcd5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-jdk7" version="2.1.0">
         <artifact name="kotlin-stdlib-jdk7-2.1.0.jar">
            <sha256 value="fdea6c4203724f42e8e64bef2f0bf79129ccd1df1edf1ccffdc22de7df498c76" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-jdk7-2.1.0.pom">
            <sha256 value="06fbc14ecb14a0af47b5a3f8d34763f4933d5cba5a3d245045bfe2f2bdb91b1e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-jdk8" version="1.6.10">
         <artifact name="kotlin-stdlib-jdk8-1.6.10.pom">
            <sha256 value="43a649fa737bfb35fa4af4e6de33e2f08a5d19104d7582ea40dbcd2b63790acc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-jdk8" version="1.8.0">
         <artifact name="kotlin-stdlib-jdk8-1.8.0.jar">
            <sha256 value="05b62804441b0c9a1920b6b7d5cf7329a4e24b6258478e32b1f046ca01900946" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-jdk8-1.8.0.pom">
            <sha256 value="2bb6c7551b97c7ba029f9866582e7aa19d63abfd4cd53da3fc0c462f3ab5fc26" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-jdk8" version="1.8.21">
         <artifact name="kotlin-stdlib-jdk8-1.8.21.pom">
            <sha256 value="3839d728d7c309a5c368b0270b44b4f1c7878ff5ca5d32a9a204faa3491459d8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-jdk8" version="1.9.10">
         <artifact name="kotlin-stdlib-jdk8-1.9.10.jar">
            <sha256 value="a4c74d94d64ce1abe53760fe0389dd941f6fc558d0dab35e47c085a11ec80f28" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-jdk8-1.9.10.pom">
            <sha256 value="5f4b94dd3065a7764c37fa15de2ad6d81f40d59f8cb33f17d181c6384fb7a72e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-stdlib-jdk8" version="2.1.0">
         <artifact name="kotlin-stdlib-jdk8-2.1.0.jar">
            <sha256 value="238d3c7e492f119b50da1c22546dd762462e55f22409611f5e53dd77625cd544" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-stdlib-jdk8-2.1.0.pom">
            <sha256 value="e762b8c45690ae8a6a35df584f54be9c9da65885e61a905426aeafca5937e1ce" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-tooling-core" version="2.1.0">
         <artifact name="kotlin-tooling-core-2.1.0.jar">
            <sha256 value="4176c612098cb92df38a485ff8b10aaa24abb400f610d48f5088aeb07c8002c8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-tooling-core-2.1.0.pom">
            <sha256 value="a5a4e9f54a413cdb2c1d140c349b559f684b0da8b0216a0a5645206f3fc581af" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlin" name="kotlin-util-io" version="2.1.0">
         <artifact name="kotlin-util-io-2.1.0.jar">
            <sha256 value="001a50db3da0a89a954db615fcb0e3f4a81a5341d407060ea88c9356f10f23e1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlin-util-io-2.1.0.pom">
            <sha256 value="ec557203d9248a3701e1665a256cf1e91691733d95fdf2f18f39e8a8ec93c7a1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlinx" name="kotlinx-serialization-bom" version="1.6.3">
         <artifact name="kotlinx-serialization-bom-1.6.3.pom">
            <sha256 value="29d69842bb7d449be2aa4ade6a4a7ce6aa55827d0ab13d168745fe6d95739332" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlinx" name="kotlinx-serialization-core" version="1.6.3">
         <artifact name="kotlinx-serialization-core-1.6.3.module">
            <sha256 value="361e9e31eb7296174b74085ac49edd84a4f3900ba9431a4e40cd1c23de76a328" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlinx" name="kotlinx-serialization-core-jvm" version="1.6.3">
         <artifact name="kotlinx-serialization-core-jvm-1.6.3.jar">
            <sha256 value="29c821a8d4e25cbfe4f2ce96cdd4526f61f8f4e69a135f9612a34a81d93b65f1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlinx-serialization-core-jvm-1.6.3.module">
            <sha256 value="329104dbd34e4bde905611d427c7584e53c3f8c411836fb9f699ac9dba5baa6c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlinx" name="kotlinx-serialization-json" version="1.6.3">
         <artifact name="kotlinx-serialization-json-1.6.3.module">
            <sha256 value="80d1d87fa0a63bff8395ea3910bda80d09f0f583504ddea8ed007bc7a86b4cd4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jetbrains.kotlinx" name="kotlinx-serialization-json-jvm" version="1.6.3">
         <artifact name="kotlinx-serialization-json-jvm-1.6.3.jar">
            <sha256 value="d3234179bcff1886d53d67c11eca47f7f3cf7b63c349d16965f6db51b7f3dd9a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kotlinx-serialization-json-jvm-1.6.3.module">
            <sha256 value="227a2a9ad38c010b107bc8058cd615177da5aaa86db37f7d58d49d9c9b7f97d0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jfrog.buildinfo" name="build-info-api" version="2.41.22">
         <artifact name="build-info-api-2.41.22.jar">
            <sha256 value="da10186c3ba43561092cbec66ce9aef81ff47de2d9d865918ff8e3cd2914c90a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="build-info-api-2.41.22.pom">
            <sha256 value="4edb2dc77d1c8c7fdeaa34f0c7133b428208cf31b15b81768e1423a71ebabc70" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jfrog.buildinfo" name="build-info-client" version="2.41.22">
         <artifact name="build-info-client-2.41.22.jar">
            <sha256 value="ce75866049e5a313908b9732394996c2596fa636cbb8dd4f4c5918102dc519b5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="build-info-client-2.41.22.pom">
            <sha256 value="e1492ab09c8f95ca9278c64d0add38a8869cd60351726fb910b05fbf29c5d3dc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jfrog.buildinfo" name="build-info-extractor" version="2.41.22">
         <artifact name="build-info-extractor-2.41.22.jar">
            <sha256 value="5091430cb08b20c4e1792bb28bf1a37a3cdf588c39891fdba3aac5d1a3f4042b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="build-info-extractor-2.41.22.pom">
            <sha256 value="260682e349d71d5128125976ccfd797b423eebf2b1ff078349224932a100b719" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jfrog.buildinfo" name="build-info-extractor-gradle" version="5.2.5">
         <artifact name="build-info-extractor-gradle-5.2.5.jar">
            <sha256 value="a06c1956fec972926f11b1f2ead50bf42366f3e36a72e86efedec887e54049d1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="build-info-extractor-gradle-5.2.5.module">
            <sha256 value="dbc9ec8efb39756c0e0d3633a536407712ced5814f6cca1f4a97f726f44e0364" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jfrog.filespecs" name="file-specs-java" version="1.1.2">
         <artifact name="file-specs-java-1.1.2.jar">
            <sha256 value="bb0dbc2c2b9fc993cfa86711398f4caf1480a9271c6d07a3a97c11b1a8e6141e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="file-specs-java-1.1.2.module">
            <sha256 value="acfb3530cc02215a186719e8c05280d02ddcf31cdac3adf827881e81e2b2e476" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jspecify" name="jspecify" version="1.0.0">
         <artifact name="jspecify-1.0.0.jar">
            <sha256 value="1fad6e6be7557781e4d33729d49ae1cdc8fdda6fe477bb0cc68ce351eafdfbab" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jspecify-1.0.0.module">
            <sha256 value="d307ca77a54e18ac1ef1aaed4e5bbe014bd2f49f29e1d2f813e47c278283195b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jspecify-1.0.0.pom">
            <sha256 value="cdab929a3b95211f43d2090c5e2d0dfe8465960e378bc32b35841dab324433a6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.10.0">
         <artifact name="junit-bom-5.10.0.module">
            <sha256 value="eb3ee6127608010694a898056e7407d117296003aba5f5db801df430b9887fcf" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.10.0.pom">
            <sha256 value="e006dd8894f9fc7b75fc32bb12fe5ed8be65667d5b454f99e2e0b8c5bb8d30b3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.10.1">
         <artifact name="junit-bom-5.10.1.module">
            <sha256 value="21b0afcfffe2ecb3770f5eb00ae7a19feaee94e771fa3918173850dae78067b7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.10.1.pom">
            <sha256 value="21c4b0286f4b20069577ff4b20978a85c100ac8a46b6f1c8672fbaab337bc3f2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.10.2">
         <artifact name="junit-bom-5.10.2.module">
            <sha256 value="de23b114b3e4119a8fe6eb17bed5a3852816698bace67071579d6d927ebb080a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.10.2.pom">
            <sha256 value="169dd904a4b0f6520cffe658cc62292bfe9f3c14a989fa92120724cde43a9968" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.10.3">
         <artifact name="junit-bom-5.10.3.module">
            <sha256 value="aa7940c9d68312e39d89a65285aa9af45f14d8f434d850ee8d93db6a56d167bb" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.10.3.pom">
            <sha256 value="10937d44c425984cb8739225d34712e1a3145641ca93ac3f7ef186fa25f6babc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.11.0">
         <artifact name="junit-bom-5.11.0.module">
            <sha256 value="f7edbe67f22042708c410abc547408e5c476f409f556236a1179221274a2ed2d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.11.0.pom">
            <sha256 value="e67459d4882424ac6374f40db1c8f4a2e88946b340ba072c80be932a2be4644d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.11.0-M1">
         <artifact name="junit-bom-5.11.0-M1.module">
            <sha256 value="8f632f8965e9b6f4069e3d58b9ea26b9a5bc76a98e89b43233777ace6dadb237" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.11.0-M1.pom">
            <sha256 value="b50975f5cba86204abda3dcd6f097af919fe22e21ef69478dd6b919f6c740d85" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.11.0-M2">
         <artifact name="junit-bom-5.11.0-M2.module">
            <sha256 value="86477abcf490d6ca059aa9973cb108d22a506f49d1a5569bb32cc6cbf43c2cce" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.11.0-M2.pom">
            <sha256 value="4a3ffc4a4edcfec2cb5d619922706a9407161798571939f854dfda73e4e17cc8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.11.2">
         <artifact name="junit-bom-5.11.2.module">
            <sha256 value="883a05b892f11859f3836de79b7207e247e14125583ccb8a3bef4d8bc0f58f2c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.11.2.pom">
            <sha256 value="f48e88538aac145eb3ae0345a9ebd055b28f329a35dce8d1e9281325ca9b0ea2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.11.3">
         <artifact name="junit-bom-5.11.3.module">
            <sha256 value="4bf0f53ceea7c790fdfbd24dba3c9e04cdc51864279eebfc57aaa473ebca0380" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.11.3.pom">
            <sha256 value="f13df2e4caf1febce567667e7c378101a0331d53d1324d6e2dfe3aecfb1f7774" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.11.4">
         <artifact name="junit-bom-5.11.4.module">
            <sha256 value="a9a4f27be94e99b9d570162d246a80f686d277d5d31aeb5481047cf51daf46e4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.11.4.pom">
            <sha256 value="19d4b747b204805325b6334553296f986562277a4ac1cb5e593a5e4c4f5e4115" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.7.1">
         <artifact name="junit-bom-5.7.1.module">
            <sha256 value="9854e3894d64b2485207e0046bca07b3d42d169e782f4fa8c9ce229a78faee04" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.7.1.pom">
            <sha256 value="0b9b14a3d62106fafe8c68a717b87b87ad18685899451b753c04fa41b6857784" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.7.2">
         <artifact name="junit-bom-5.7.2.module">
            <sha256 value="f3bceb1c59dd4f6993f4304dffa580172b8df65a76cd36fa4fd92c0578d28ad8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.7.2.pom">
            <sha256 value="cd14aaa869991f82021c585d570d31ff342bcba58bb44233b70193771b96487b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.8.2">
         <artifact name="junit-bom-5.8.2.module">
            <sha256 value="40cfad993fa70ecdf2af74d0c56da1484ee220964be8f932cfe632be9a2733fa" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.8.2.pom">
            <sha256 value="836069ca9e8ee3c56e48376222da291263f137bd3fd16d84fdd47efcc3f286e2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.9.0">
         <artifact name="junit-bom-5.9.0.module">
            <sha256 value="a054eaf5016b58bbcde86660444a7c0dd3e2caf84d2a1ad5fc4cb525979c19b9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.9.0.pom">
            <sha256 value="d83e87f1676cde44191eec5cda690492392f26923b1e498148ca739dfed75295" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.9.1">
         <artifact name="junit-bom-5.9.1.module">
            <sha256 value="9026c1656690fa145ad75ece83674211aa12ad8930a91b107c2f5cdece2f1b1c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.9.1.pom">
            <sha256 value="b163c1cfc8fc1fd58b457a00d586c04c46e986d75904e9ca54c03a97d65b496c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit" name="junit-bom" version="5.9.3">
         <artifact name="junit-bom-5.9.3.module">
            <sha256 value="b401fd25901e582a524aa5343c4b39e28bc56e24961c1069bf2b4bbfcee46b93" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-bom-5.9.3.pom">
            <sha256 value="4d0329cd9e72f2420e5ca15724cbfe6ffa6e5fd2888361516271190fdc342ed7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.jupiter" name="junit-jupiter" version="5.11.4">
         <artifact name="junit-jupiter-5.11.4.jar">
            <sha256 value="aa880e4afba87d447357e4c1fc098c5cb1d200cb9403496c00d3b35a5bd0e8db" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-jupiter-5.11.4.module">
            <sha256 value="a3c353ed7516ae1835eab95a087f4f09a5b0cc7d0b8b039fe77a039b8ab52103" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-jupiter-5.11.4.pom">
            <sha256 value="a862df11ee539982efcb141d9c0ade7037bba0265d0b589d5fb84db0d212f1d9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.jupiter" name="junit-jupiter-api" version="5.11.4">
         <artifact name="junit-jupiter-api-5.11.4.jar">
            <sha256 value="ab83ef9e51ac4597d59d26b4b58812129550e2f579a404c8af7d09f5ce5b4293" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-jupiter-api-5.11.4.module">
            <sha256 value="a6ea2fefb3aa5868fd7a780ae1da1162e76dda3760b48015c2a419d2372ff3cb" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-jupiter-api-5.11.4.pom">
            <sha256 value="512d23ff39c76665a1a3645526230bcf889bd49884304f7febe047b018eeb339" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.jupiter" name="junit-jupiter-api" version="5.5.2">
         <artifact name="junit-jupiter-api-5.5.2.jar">
            <sha256 value="249a2fdbd3931987c0298d00ca08ed248496e0fc11e0463c08c4f82e0cc79b1c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-jupiter-api-5.5.2.pom">
            <sha256 value="76a15d80d8587a1a5a3a5b142d2ceb37304fbd91198ba425486e57f76d088860" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.jupiter" name="junit-jupiter-engine" version="5.11.4">
         <artifact name="junit-jupiter-engine-5.11.4.jar">
            <sha256 value="cdf8ac59f3fad774ca738ad03890950eeb91833ef0e8908753177edd26f1581c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-jupiter-engine-5.11.4.module">
            <sha256 value="db91163a8af005a32798565dd67537725189590deab6da03b31dbdda45686a18" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-jupiter-engine-5.11.4.pom">
            <sha256 value="b0a323b0d03444441d13d4630b40db5ef84160d2c2f585d4d646dc3882f99207" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.jupiter" name="junit-jupiter-engine" version="5.5.2">
         <artifact name="junit-jupiter-engine-5.5.2.jar">
            <sha256 value="6d777da9876e2ef7a0336e8f098f8d74a5a64f810aa3a4a2f5f3b766ce97837b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-jupiter-engine-5.5.2.pom">
            <sha256 value="032fef4e2563084d397eae481a388010f443401af6252bfd377d76307f6f8bfe" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.jupiter" name="junit-jupiter-engine" version="5.8.2">
         <artifact name="junit-jupiter-engine-5.8.2.module">
            <sha256 value="a56204c716c2379970ca8e3fe2a72e3a030cd9027328d3ce1457dd10c0150e7e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.jupiter" name="junit-jupiter-params" version="5.11.4">
         <artifact name="junit-jupiter-params-5.11.4.jar">
            <sha256 value="02a6e015de7ce94ac7f256e7fa05b8091dea861fe79a555a7993313d0f6c7d96" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-jupiter-params-5.11.4.module">
            <sha256 value="58865b8b4b998b9ab19c13f8e413b7e1d4819374dd5df69760c4c0b0bc4e9895" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-jupiter-params-5.11.4.pom">
            <sha256 value="8c359bbea07c64a6acdd8c1e13b4f77058b2432b35e86e9a95ddc63249a0ad54" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.platform" name="junit-platform-commons" version="1.11.4">
         <artifact name="junit-platform-commons-1.11.4.jar">
            <sha256 value="9edd969b0d0670c54105bc91ae79bd1c6f503e12115faba82073b84c86bbc334" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-platform-commons-1.11.4.module">
            <sha256 value="0b9e2625c8f468b3cd4132cc0a805f89fe41f852d1adfff75f3eb1465ca1cb6b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-platform-commons-1.11.4.pom">
            <sha256 value="cd12d2b7c242f165548ed9c9406160dcedb6080925b47cf731e0fdae5fb4bce2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.platform" name="junit-platform-commons" version="1.5.2">
         <artifact name="junit-platform-commons-1.5.2.jar">
            <sha256 value="fc44afdfc0f20c85e71a66e7943281aef3bc1e0fd62d2d69a36cb6901e682c10" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-platform-commons-1.5.2.pom">
            <sha256 value="3bd0d4ded632a8af8ca587fb6764019de771b1d6bcb89acd562435a1009faada" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.platform" name="junit-platform-console" version="1.10.0">
         <artifact name="junit-platform-console-1.10.0.module">
            <sha256 value="6b16719414a0d52bd97b33d5eb6e7eefae5ed610dc8c436a0de8e0c341bb6480" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.platform" name="junit-platform-console" version="1.11.4">
         <artifact name="junit-platform-console-1.11.4.jar">
            <sha256 value="a9c3309cdfded3542200de85da6cb274864439d6b02ba80bb45ecc8e0bdf1be7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-platform-console-1.11.4.module">
            <sha256 value="02fda1c87cf9150bd8d25fb771c841f55460fee25337d8e6ff4b5ca847b778a7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.platform" name="junit-platform-engine" version="1.11.4">
         <artifact name="junit-platform-engine-1.11.4.jar">
            <sha256 value="b1dd998f64f9acadc15966d9cd3d08074662677b3e390f0a38fcbf0bb4c72330" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-platform-engine-1.11.4.module">
            <sha256 value="bf6ce1fb5951dc6c7de36adeef6aeaf78ef82d63831c5ccebce388da9fc5fe25" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-platform-engine-1.11.4.pom">
            <sha256 value="943471579984212fbe69d03edec7c2ff4fbab1888be0b80c265ea70869fd8abd" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.platform" name="junit-platform-engine" version="1.5.2">
         <artifact name="junit-platform-engine-1.5.2.jar">
            <sha256 value="ff20ba4ad8c00ef17baef9c55512f9c02d9a68740f7f1ac01a9a6aa0239931f8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-platform-engine-1.5.2.pom">
            <sha256 value="2d4b95555c21e085ebc1ddbdf42d79eb1d55640dc193b1b7e6100240fd2f189f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.platform" name="junit-platform-launcher" version="1.10.0">
         <artifact name="junit-platform-launcher-1.10.0.module">
            <sha256 value="f5dcb7408c4242603f99bd5cdc635c4066d18a828db85ed3700aa8dd3eaf1a69" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.platform" name="junit-platform-launcher" version="1.11.4">
         <artifact name="junit-platform-launcher-1.11.4.jar">
            <sha256 value="d7430bd029e7fcced53ee445e4d2d1a8a1e043ea4c4df43b6335a857f79761ae" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-platform-launcher-1.11.4.module">
            <sha256 value="60c4533209ba587b1cda7d72c087f351a3231f9b91c94f3f76394b0e22341cc4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.platform" name="junit-platform-launcher" version="1.5.2">
         <artifact name="junit-platform-launcher-1.5.2.jar">
            <sha256 value="c1f6473ddd343550b42e94072aff9417ff38398f8b6bafe27b785aebcdaa660b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-platform-launcher-1.5.2.pom">
            <sha256 value="25a42980af3a24685376633e511a928f8e83de736fbc6fd46eac2238d10a3631" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.platform" name="junit-platform-reporting" version="1.11.4">
         <artifact name="junit-platform-reporting-1.11.4.jar">
            <sha256 value="df6896109bfaef4de8d2fa9e3371a6176936d1a45a6c0e7fd8f7e6dd6f4c5597" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-platform-reporting-1.11.4.module">
            <sha256 value="077f390e71042ff10e2ced70d688d6d4a925eabcc1f505c4dfd8d18209394e32" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.junit.vintage" name="junit-vintage-engine" version="5.5.2">
         <artifact name="junit-vintage-engine-5.5.2.jar">
            <sha256 value="350c19f11a1112c0f0cae7701f7e2cbbd2247d42460da096fa515a25b4a9754d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="junit-vintage-engine-5.5.2.pom">
            <sha256 value="7aac894933943af601fcde32e34a9825732e204d09d48b2b3b5cb086dc736b8d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jupnp" name="jupnp" version="3.0.3">
         <artifact name="jupnp-3.0.3.pom">
            <sha256 value="1054bf2c7053e0a510430b9203022c4e8e68fd7de70481c347ff365a2d12963d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jupnp" name="org.jupnp" version="3.0.3">
         <artifact name="org.jupnp-3.0.3.jar">
            <sha256 value="a538ea0dc0d53a4b69203555c97b65c980fb36e8a80e83f7813af770ad4ee840" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.jupnp-3.0.3.pom">
            <sha256 value="6221580233ea71faec7266a660f1fc6f8351aca621be64947bb8ac68a4a4766c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jupnp" name="org.jupnp.support" version="3.0.3">
         <artifact name="org.jupnp.support-3.0.3.jar">
            <sha256 value="8160cab958920bc96d0575da18396fa3ecbd0e4de6120e26e3b93dbfb764bdb4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.jupnp.support-3.0.3.pom">
            <sha256 value="bde0b0eeefe83423134b5a14101fdf783484dff494851cf08badc638a2b04382" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.jupnp.pom" name="bundles" version="3.0.3">
         <artifact name="bundles-3.0.3.pom">
            <sha256 value="c5ed8b5462229465c3192270a3a05a0f2aaf5555b4cd650ad48b0ac98cbab545" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.mockito" name="mockito-bom" version="4.11.0">
         <artifact name="mockito-bom-4.11.0.pom">
            <sha256 value="d8531a746c988f7f68ed5f188cdea945006aea993ec5df9e524e0d27d61491da" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.mockito" name="mockito-bom" version="5.15.2">
         <artifact name="mockito-bom-5.15.2.pom">
            <sha256 value="8a5e77d84208eb2f208f9203df768c63a7913ca17cb463802fe4d3d6e0d136cc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.mockito" name="mockito-core" version="5.15.2">
         <artifact name="mockito-core-5.15.2.jar">
            <sha256 value="bf48b7372d9491d5ec8aebb4cdd187d15663931599c0fbe7410166ce0e1e58ff" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="mockito-core-5.15.2.pom">
            <sha256 value="fed812d2a2f39c98474b2a14c63c65f8f471783b6082dc867c0c1b6053c69480" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.mockito" name="mockito-junit-jupiter" version="5.15.2">
         <artifact name="mockito-junit-jupiter-5.15.2.jar">
            <sha256 value="4e82b710a47a0034a1a64190021078a3aaa982dd29b2a0bd69cbf72c8d9d239a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="mockito-junit-jupiter-5.15.2.pom">
            <sha256 value="dab01b90f1f1ba2e99a9b75cee240903a004177f91cb7cf211e5f4fdc86a1e03" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.mortbay.jasper" name="apache-el" version="9.0.83.1">
         <artifact name="apache-el-9.0.83.1.jar">
            <sha256 value="e88b040b6a2f103d10f5289284b8291dad990d81bded1d64778670bf93bcaac7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="apache-el-9.0.83.1.pom">
            <sha256 value="7db9adcbe3a719403b0eb546939bb268c69ce2aa7c36b73eba615fc9893b708b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.mortbay.jasper" name="apache-jsp" version="9.0.83.1">
         <artifact name="apache-jsp-9.0.83.1.jar">
            <sha256 value="550bde30012ff3a56bcec05723ad7424bb8f65ac80d5ee69fd8d41c4ff5f2f75" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="apache-jsp-9.0.83.1.pom">
            <sha256 value="ae663476c09f024b07d16e2bd5feea2a75e83bb327244a822aae1b5c7c817f83" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.mortbay.jasper" name="jasper-jsp" version="9.0.83.1">
         <artifact name="jasper-jsp-9.0.83.1.pom">
            <sha256 value="3a364f72c9e66fd3d5a02c5e1925c0d381f74eb1ccdf8024adfed92ef06dd3af" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.objenesis" name="objenesis" version="3.3">
         <artifact name="objenesis-3.3.jar">
            <sha256 value="02dfd0b0439a5591e35b708ed2f5474eb0948f53abf74637e959b8e4ef69bfeb" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="objenesis-3.3.pom">
            <sha256 value="ba0c40da2669a048b6e24ef7066a471f0fbcbfcc509e6a3e856ca4ddfa614ad3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.objenesis" name="objenesis-parent" version="3.3">
         <artifact name="objenesis-parent-3.3.pom">
            <sha256 value="305c384aa2f1e1c7fe53a96da41c3ec35243b97d428d24a8f779818cc10be4ff" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.openjdk.jmh" name="jmh-core" version="1.36">
         <artifact name="jmh-core-1.36.jar">
            <sha256 value="f90974e37d0da8886b5c05e6e3e7e20556900d747c5a41c1023b47c3301ea73c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jmh-core-1.36.pom">
            <sha256 value="981234265ac041f166b17c5adc6b1d459e894cabffeaf6b09c0074f0f40382e4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.openjdk.jmh" name="jmh-core" version="1.37">
         <artifact name="jmh-core-1.37.jar">
            <sha256 value="dc0eaf2bbf0036a70b60798c785d6e03a9daf06b68b8edb0f1ba9eb3421baeb3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jmh-core-1.37.pom">
            <sha256 value="04453be006f06f86d7c43f3c492f7b4eb3362680cae4f1ee80ba65db23373f5a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.openjdk.jmh" name="jmh-generator-annprocess" version="1.37">
         <artifact name="jmh-generator-annprocess-1.37.jar">
            <sha256 value="6a5604b5b804e0daca1145df1077609321687734a8b49387e49f10557c186c77" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jmh-generator-annprocess-1.37.pom">
            <sha256 value="e4240265b5425c39f1cf2733afda3aec3b139dd193e794d55137bec9240ff476" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.openjdk.jmh" name="jmh-generator-asm" version="1.36">
         <artifact name="jmh-generator-asm-1.36.jar">
            <sha256 value="7460b11b823dee74b3e19617d35d5911b01245303d6e31c30f83417cfc2f54b5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jmh-generator-asm-1.36.pom">
            <sha256 value="57bbbe878394f8b3fc1b80c41161dd488b42c0d7b3d70a5e474f4271b1ea3a49" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.openjdk.jmh" name="jmh-generator-bytecode" version="1.36">
         <artifact name="jmh-generator-bytecode-1.36.jar">
            <sha256 value="3376542f8950bcd706c516ce0620d1df822309e7092b8f9338917283181d585d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jmh-generator-bytecode-1.36.pom">
            <sha256 value="5110011d25fb9466b2f009bff7d7ec0a62e8a7abaf06899bfb6b6305b04e2944" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.openjdk.jmh" name="jmh-generator-reflection" version="1.36">
         <artifact name="jmh-generator-reflection-1.36.jar">
            <sha256 value="a9c72760e12c199e2a2c28f1a126ebf0cc5b51c0b58d46472596fc32f7f92534" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jmh-generator-reflection-1.36.pom">
            <sha256 value="9ac088332ca9d14713089939e73cda1e7ff443c7c8c77bac885b08a3b7875146" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.openjdk.jmh" name="jmh-parent" version="1.36">
         <artifact name="jmh-parent-1.36.pom">
            <sha256 value="3b63ce6e8fefacb320376e05e9fbb3bae86a889239008759189a0b0d5ca5c5d6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.openjdk.jmh" name="jmh-parent" version="1.37">
         <artifact name="jmh-parent-1.37.pom">
            <sha256 value="0c24f216f3637dde7639114f70273a697f8546f7a4c6d5acd4cc6daee9bef4c9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.openjdk.jol" name="jol-core" version="0.17">
         <artifact name="jol-core-0.17.jar">
            <sha256 value="bd73d9ad265d8478ccde06130200877f3a4f0a6e2d44e7fb8cbb2e6f4104cbdc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="jol-core-0.17.pom">
            <sha256 value="1abac2bcdd59c65bde95aa254bfc7da2e030080a6ace0a9deeb1a396bd38ecfe" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.openjdk.jol" name="jol-parent" version="0.17">
         <artifact name="jol-parent-0.17.pom">
            <sha256 value="e13ef86564da581de9383f9d4d1bf8c132b7c8689713308fc9f3c3e5e2e786c0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.opentest4j" name="opentest4j" version="1.2.0">
         <artifact name="opentest4j-1.2.0.jar">
            <sha256 value="58812de60898d976fb81ef3b62da05c6604c18fd4a249f5044282479fc286af2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentest4j-1.2.0.pom">
            <sha256 value="a96e671816c1ff8803bdec74c9241f025bdfb277da5d2b4ee02266405936f994" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.opentest4j" name="opentest4j" version="1.3.0">
         <artifact name="opentest4j-1.3.0.jar">
            <sha256 value="48e2df636cab6563ced64dcdff8abb2355627cb236ef0bf37598682ddf742f1b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentest4j-1.3.0.module">
            <sha256 value="48bf1d6c8b5dc94f74652bd17900f654deb714350248cf5e8fca27b9090c8e0d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="opentest4j-1.3.0.pom">
            <sha256 value="9bf7cffc410f3e8372c2522578df9ca56d9d43bd937e30948706c232a943b355" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.osgi" name="org.osgi.service.cm" version="1.6.1">
         <artifact name="org.osgi.service.cm-1.6.1.jar">
            <sha256 value="529da7b6806af4d788acc0aef0079c9c920f6cb1d2679c8f392ca552fb2e1d35" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.osgi.service.cm-1.6.1.pom">
            <sha256 value="7b2d78dbab39da202f4f2528350bb016bb457ccf764a35f03157e2923b97fdc8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.osgi" name="org.osgi.service.component" version="1.5.1">
         <artifact name="org.osgi.service.component-1.5.1.jar">
            <sha256 value="0653852deab5c6f6e9c84e3aa8f72e72b9ce7dfaf7a6431f3d80f6f9d7a433ad" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.osgi.service.component-1.5.1.pom">
            <sha256 value="9edc17a6148f2a015e9cba5bf0ab824890c623ec58b373e5c7d475d6dfde9989" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.osgi" name="org.osgi.service.event" version="1.4.1">
         <artifact name="org.osgi.service.event-1.4.1.jar">
            <sha256 value="9f11c68980dbd931850f3f27fc7c35e1d710ecc728fe872bd6d786cd7e4b7b5e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.osgi.service.event-1.4.1.pom">
            <sha256 value="51495ce540f383ef63f191183999ddf43f81f7357b111b7f2cb706a7bbe1caf8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.osgi" name="org.osgi.service.metatype" version="1.4.1">
         <artifact name="org.osgi.service.metatype-1.4.1.jar">
            <sha256 value="83d9cc2b62500d2e438be91c157659c2d693a262cfd14eb9b916286ca627ed00" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.osgi.service.metatype-1.4.1.pom">
            <sha256 value="c5ecb707347a1b19046792f542920f1b57c971d83f0cc7e78a86fc1cb68615da" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.osgi" name="org.osgi.service.prefs" version="1.1.2">
         <artifact name="org.osgi.service.prefs-1.1.2.jar">
            <sha256 value="43c7c870710e363405d422da653cce0d798a4537f76e4930f79bceadd3a55345" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.osgi.service.prefs-1.1.2.pom">
            <sha256 value="6ea483cea177e91310984c6452e694aa20801951b501f17cb9ba114328c2cc26" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.osgi" name="org.osgi.util.function" version="1.2.0">
         <artifact name="org.osgi.util.function-1.2.0.jar">
            <sha256 value="208819c7c71690c15a6bb8b187474e7f9d0147946b680182a62b9f222ae014ec" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.osgi.util.function-1.2.0.pom">
            <sha256 value="f4edd84181157d436818d46565d480379c1b07001d5cb130b406719729115ea8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.osgi" name="org.osgi.util.promise" version="1.3.0">
         <artifact name="org.osgi.util.promise-1.3.0.jar">
            <sha256 value="7053c57e7d7d88fec6b90979a3af125e1d2bb847268a328a2f1ed65ad0a4c185" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="org.osgi.util.promise-1.3.0.pom">
            <sha256 value="adcc8af5c7be67b052105e0c9dcab8de26dac6f19bc5a9ab72946a33276c7100" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2" name="ow2" version="1.5">
         <artifact name="ow2-1.5.pom">
            <sha256 value="0f8a1b116e760b8fe6389c51b84e4b07a70fc11082d4f936e453b583dd50b43b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2" name="ow2" version="1.5.1">
         <artifact name="ow2-1.5.1.pom">
            <sha256 value="321ddbb7ee6fe4f53dea6b4cd6db74154d6bfa42391c1f763b361b9f485acf05" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm" version="9.0">
         <artifact name="asm-9.0.jar">
            <sha256 value="0df97574914aee92fd349d0cb4e00f3345d45b2c239e0bb50f0a90ead47888e0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-9.0.module">
            <sha256 value="8af81096ed3affa39a4729fc900a55b663894911d67c4d4bef0ea424393dd3f9" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm" version="9.2">
         <artifact name="asm-9.2.jar">
            <sha256 value="b9d4fe4d71938df38839f0eca42aaaa64cf8b313d678da036f0cb3ca199b47f5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-9.2.pom">
            <sha256 value="dfb12a1b224bf01be1fd604020466f894241bcb645dcce395edd8cd6f8a50df4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm" version="9.6">
         <artifact name="asm-9.6.jar">
            <sha256 value="3c6fac2424db3d4a853b669f4e3d1d9c3c552235e19a319673f887083c2303a1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-9.6.pom">
            <sha256 value="92eee24bc3c843e4881d46c1dd6505471ee3142facfb466b428cfea5a56c6b60" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm" version="9.7">
         <artifact name="asm-9.7.jar">
            <sha256 value="adf46d5e34940bdf148ecdd26a9ee8eea94496a72034ff7141066b3eea5c4e9d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-9.7.pom">
            <sha256 value="de00115f1d84f3a0b2ee3a4b6f6192d066f86d185d67b9d1522f2c80feac5f00" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm-analysis" version="9.2">
         <artifact name="asm-analysis-9.2.jar">
            <sha256 value="878fbe521731c072d14d2d65b983b1beae6ad06fda0007b6a8bae81f73f433c4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-analysis-9.2.pom">
            <sha256 value="773cc1a2bfc14c6c4a979c51a075c0234a0bf694fc3abe4facf454f37a145f1b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm-bom" version="9.6">
         <artifact name="asm-bom-9.6.pom">
            <sha256 value="8a0e5f624fe2930b7a8d69956f438e45ef532996b4d64409b616c4aef4b1ac4e" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm-bom" version="9.7">
         <artifact name="asm-bom-9.7.pom">
            <sha256 value="8c8651f3be043b3578dd28a15c0161861b15eb039b7f5247679c0cc0dbf07787" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm-commons" version="9.2">
         <artifact name="asm-commons-9.2.jar">
            <sha256 value="be4ce53138a238bb522cd781cf91f3ba5ce2f6ca93ec62d46a162a127225e0a6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-commons-9.2.pom">
            <sha256 value="02824e839f2a2f0e72959fdd30b4897240f05afc43de42d7ba0b18437601c070" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm-commons" version="9.6">
         <artifact name="asm-commons-9.6.jar">
            <sha256 value="7aefd0d5c0901701c69f7513feda765fb6be33af2ce7aa17c5781fc87657c511" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-commons-9.6.pom">
            <sha256 value="a98ae4895334baf8ff86bd66516210dbd9a03f1a6e15e47dda82afcf6b53d77c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm-commons" version="9.7">
         <artifact name="asm-commons-9.7.jar">
            <sha256 value="389bc247958e049fc9a0408d398c92c6d370c18035120395d4cba1d9d9304b7a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-commons-9.7.pom">
            <sha256 value="5acee3ee7252ed90b8074c755d022787499a95fafff98ac4a685107c4da409b4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm-tree" version="9.2">
         <artifact name="asm-tree-9.2.jar">
            <sha256 value="aabf9bd23091a4ebfc109c1f3ee7cf3e4b89f6ba2d3f51c5243f16b3cffae011" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-tree-9.2.pom">
            <sha256 value="f61f3ebea5520ddf19f452b03c426c7231bdd8a81d7ac28765cb5271225ac378" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm-tree" version="9.6">
         <artifact name="asm-tree-9.6.jar">
            <sha256 value="c43ecf17b539c777e15da7b5b86553b377e2d39a683de6285567d5283888e7ef" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-tree-9.6.pom">
            <sha256 value="1bcb481d7fc16b955bb60ca07c8cfa2424bcee78bdc405bba31c7d6f5dc2d113" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm-tree" version="9.7">
         <artifact name="asm-tree-9.7.jar">
            <sha256 value="62f4b3bc436045c1acb5c3ba2d8ec556ec3369093d7f5d06c747eb04b56d52b1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-tree-9.7.pom">
            <sha256 value="a34ea1e3e4128c01038db43c6976e88c779cf5af84b0505da266dfe6965668ec" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.ow2.asm" name="asm-util" version="9.2">
         <artifact name="asm-util-9.2.jar">
            <sha256 value="ff5b3cd331ae8a9a804768280da98f50f424fef23dd3c788bb320e08c94ee598" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="asm-util-9.2.pom">
            <sha256 value="ddd06913f147d70ae68e7a6e4356a55b33f14dde6162dbff2bd0e289581f1ad2" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.owasp.encoder" name="encoder" version="1.3.1">
         <artifact name="encoder-1.3.1.jar">
            <sha256 value="c9c56c8970c7cb11b231913ba5190ce930f8fd4fac2bd918810642dc3848e757" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="encoder-1.3.1.pom">
            <sha256 value="29bda1dd64e8490cd8e01d12b802c8db2df7e1ba83e3109b12924e5f0901500b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.owasp.encoder" name="encoder-parent" version="1.3.1">
         <artifact name="encoder-parent-1.3.1.pom">
            <sha256 value="bc30ddc036c1fe27982d88deb29847ab3e7b3b5655136c277d86741d55acae38" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.pcollections" name="pcollections" version="4.0.1">
         <artifact name="pcollections-4.0.1.jar">
            <sha256 value="1f82766d7c3221930854033bebff5073ea46b43f27326074bbe15d148c18bfb3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="pcollections-4.0.1.module">
            <sha256 value="9ebda7c0c9db1134d341dff7c8ad0bc5953286fbf0b998aeca297a1ee6e809f0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.reactivestreams" name="reactive-streams" version="1.0.3">
         <artifact name="reactive-streams-1.0.3.jar">
            <sha256 value="1dee0481072d19c929b623e155e14d2f6085dc011529a0a0dbefc84cf571d865" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="reactive-streams-1.0.3.pom">
            <sha256 value="cced467175f4257833f6cb07510ff97b3c75a06e1a58d882a39d79853d51c602" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.reactivestreams" name="reactive-streams" version="1.0.4">
         <artifact name="reactive-streams-1.0.4.jar">
            <sha256 value="f75ca597789b3dac58f61857b9ac2e1034a68fa672db35055a8fb4509e325f28" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="reactive-streams-1.0.4.pom">
            <sha256 value="54ba23d87a2d438540c99ef8794a0856fc573a256b498678283c3c67ef18ada8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.rocksdb" name="rocksdbjni" version="9.7.3">
         <artifact name="rocksdbjni-9.7.3.jar">
            <sha256 value="5ca63e0e955f101f7af1c1fb8ce260b5d3a5701cea7d8c2852b9d56031a57221" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="rocksdbjni-9.7.3.pom">
            <sha256 value="d0948ea3d72dd447e980bc495d27ec2208ecb298f7a066c8fe0e1425cd9efca0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.slf4j" name="slf4j-api" version="1.7.36">
         <artifact name="slf4j-api-1.7.36.pom">
            <sha256 value="fb046a9c229437928bb11c2d27c8b5d773eb8a25e60cbd253d985210dedc2684" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.slf4j" name="slf4j-api" version="2.0.13">
         <artifact name="slf4j-api-2.0.13.jar">
            <sha256 value="e7c2a48e8515ba1f49fa637d57b4e2f590b3f5bd97407ac699c3aa5efb1204a9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="slf4j-api-2.0.13.pom">
            <sha256 value="51805cfda80ca2ac82041b906d9865d39e9823e358a0eeb62379dfed475c1571" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.slf4j" name="slf4j-api" version="2.0.16">
         <artifact name="slf4j-api-2.0.16.jar">
            <sha256 value="a12578dde1ba00bd9b816d388a0b879928d00bab3c83c240f7013bf4196c579a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="slf4j-api-2.0.16.pom">
            <sha256 value="b1a00f5b1c4dbe62b805d65d23911a6f77063889d7cb1e86fe8389d6190473f7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.slf4j" name="slf4j-api" version="2.0.7">
         <artifact name="slf4j-api-2.0.7.pom">
            <sha256 value="2d403ccf0e0a02d5c1a8667b0e2a33c8dfc6038ab287b9671dd681c205267981" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.slf4j" name="slf4j-bom" version="2.0.13">
         <artifact name="slf4j-bom-2.0.13.pom">
            <sha256 value="7af272d7a738e2b98763791ffdd896040e8beb298a88fd606214407976cd310a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.slf4j" name="slf4j-bom" version="2.0.16">
         <artifact name="slf4j-bom-2.0.16.pom">
            <sha256 value="0566048ec825cdf28758620af64d9e14ae38a9cd8748dd6fafa6df4a4194c279" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.slf4j" name="slf4j-nop" version="2.0.16">
         <artifact name="slf4j-nop-2.0.16.jar">
            <sha256 value="deca6c04ed35515a0a911fa44c0e836bee92c0c59d2e8fa9bab8ffbc464a9ba7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="slf4j-nop-2.0.16.pom">
            <sha256 value="96d538b89508cc781afa1d3334c891519aaaaf5cbcb43a349198fc2a5abb0f1c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.slf4j" name="slf4j-parent" version="1.7.36">
         <artifact name="slf4j-parent-1.7.36.pom">
            <sha256 value="bb388d37fbcdd3cde64c3cede21838693218dc451f04040c5df360a78ed7e812" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.slf4j" name="slf4j-parent" version="2.0.13">
         <artifact name="slf4j-parent-2.0.13.pom">
            <sha256 value="67facfd51f06935cea85615a04775d70d80bfd03ad0f37670351f920ed0bb58a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.slf4j" name="slf4j-parent" version="2.0.16">
         <artifact name="slf4j-parent-2.0.16.pom">
            <sha256 value="09a0b4cc814d7274616c9b16d4c0fd9aaf1ecc813334de5131e547271b1982e5" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.slf4j" name="slf4j-parent" version="2.0.7">
         <artifact name="slf4j-parent-2.0.7.pom">
            <sha256 value="c182bb36cd3af1c93c1603cdfefe7889157db30ba8b584f4a44535fcd22e45e7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.sonarqube" name="org.sonarqube.gradle.plugin" version="6.2.0.5505">
         <artifact name="org.sonarqube.gradle.plugin-6.2.0.5505.pom">
            <sha256 value="ad15fa6ac0e40aa7e09892f4e393c3c8847aef9f3ac117670b7081362b6eef02" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.sonarsource.parent" name="parent" version="83.0.0.2369">
         <artifact name="parent-83.0.0.2369.pom">
            <sha256 value="d351a6587192b3c37ef3c5525b8319fef13833b630099081c027e21d63337efd" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.sonarsource.scanner.gradle" name="sonarqube-gradle-plugin" version="6.2.0.5505">
         <artifact name="sonarqube-gradle-plugin-6.2.0.5505.jar">
            <sha256 value="00049e1eb220f71936d3545dd2688fcff7dc3861ebac0ff1517cdbfc075fcecc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="sonarqube-gradle-plugin-6.2.0.5505.module">
            <sha256 value="6a5782114323d3ea07ef63357aba6c94caad5ee7c2d1a5e9132655942d2a39ed" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.sonarsource.scanner.lib" name="sonar-scanner-java-library" version="3.3.1.450">
         <artifact name="sonar-scanner-java-library-3.3.1.450.jar">
            <sha256 value="cd2941a71a705a96c181e10903f2e4f787087eb7e49d99b6dada408dc5dd9176" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="sonar-scanner-java-library-3.3.1.450.pom">
            <sha256 value="023d77e24e5bfe3c150e002579e5684c101d5c1dc7a860173ed2fedcbd615227" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.sonarsource.scanner.lib" name="sonar-scanner-java-library-batch-interface" version="3.3.1.450">
         <artifact name="sonar-scanner-java-library-batch-interface-3.3.1.450.jar">
            <sha256 value="adbfef2053957a4e9639db69730ba83a090a5f8fa1cc3424a122c2c6663297d9" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="sonar-scanner-java-library-batch-interface-3.3.1.450.pom">
            <sha256 value="b3d7ba47dfc2de1cc9e4cf062dfc5017a3b2e3466d36098e61ce9f54147b85f4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.sonarsource.scanner.lib" name="sonar-scanner-java-library-parent" version="3.3.1.450">
         <artifact name="sonar-scanner-java-library-parent-3.3.1.450.pom">
            <sha256 value="7d73eda3f20a591a479b656e4c6d5623a37405672ac3e2cbf50b1a0feb374c49" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.sonatype.oss" name="oss-parent" version="5">
         <artifact name="oss-parent-5.pom">
            <sha256 value="1678d4120a585d8a630131aeec4c524d928398583b7eab616ee7d5a87f520d3d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.sonatype.oss" name="oss-parent" version="7">
         <artifact name="oss-parent-7.pom">
            <sha256 value="b51f8867c92b6a722499557fc3a1fdea77bdf9ef574722fe90ce436a29559454" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.sonatype.oss" name="oss-parent" version="9">
         <artifact name="oss-parent-9.pom">
            <sha256 value="fb40265f982548212ff82e362e59732b2187ec6f0d80182885c14ef1f982827a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.springframework" name="spring-framework-bom" version="5.3.32">
         <artifact name="spring-framework-bom-5.3.32.module">
            <sha256 value="47a7091f697ca631185ed418f0ca2b86eef0c82aa16d1771ddec4b2c3bb7b3bd" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="spring-framework-bom-5.3.32.pom">
            <sha256 value="825af7112e3d92ea436bb0df78c19514885f5c016d0fc60c8b24d4763a015a10" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.springframework" name="spring-framework-bom" version="5.3.39">
         <artifact name="spring-framework-bom-5.3.39.module">
            <sha256 value="f88b40e2a50333b40b42f181eee272b75ec75dd8666cb145bd90b15b97e183e3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="spring-framework-bom-5.3.39.pom">
            <sha256 value="f6d481093e75767cba1ac7e1db38f8f692cbe3e38744693371a75aeb21c6148b" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.springframework.security" name="spring-security-crypto" version="6.4.4">
         <artifact name="spring-security-crypto-6.4.4.jar">
            <sha256 value="62b3424be620e457146a51a3da9aa878ee9874667cd2498d73085a8394d36940" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="spring-security-crypto-6.4.4.module">
            <sha256 value="db4e292ef3100081dcd9338b09cb3e94a13bd6fa95dedfe0232f6896c92cbd78" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="spring-security-crypto-6.4.4.pom">
            <sha256 value="79bb9a97ea5d3c45fdc4731ea6ac69b2c2ffff4c13c2341f58503a4b6a23b1ce" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.tukaani" name="xz" version="1.9">
         <artifact name="xz-1.9.jar">
            <sha256 value="211b306cfc44f8f96df3a0a3ddaf75ba8c5289eed77d60d72f889bb855f535e5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="xz-1.9.pom">
            <sha256 value="093be1b03331bce2932d6825c37e98272d7621e6a9e9fb93289a002518b8dd5a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="abi" version="4.12.3">
         <artifact name="abi-4.12.3.jar">
            <sha256 value="db00551c5d4f7461d6d80834073ff1b30713082715afff547875493be7b81564" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="abi-4.12.3.module">
            <sha256 value="a84224b355fb96c817f451ce45c760f58f1175358ae0add03dfca917adfd29a7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="abi-4.12.3.pom">
            <sha256 value="79317d7db3c4d29248fc3e4ba569568cce62b92f7c4243cd1a81df48a2fcb965" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="abi" version="4.14.0">
         <artifact name="abi-4.14.0.jar">
            <sha256 value="bfdbec6bb51b335b7b752e5963ef5646d8df05cdbdc8480f0651a109ac2984a6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="abi-4.14.0.module">
            <sha256 value="e400acb9ab484ad5727c008a4e41f4da5d9e3228b4b9b3083ef07dcb04a0f8ca" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="abi-4.14.0.pom">
            <sha256 value="e126c729af686a9d683e6f1866249b6292459455fef1af6e06c5adca2ddfa90c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="besu" version="4.12.3">
         <artifact name="besu-4.12.3.jar">
            <sha256 value="acf8e145a18844997b2a72ef73811f587893cb018e936110c67ec4925fd33ec1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="besu-4.12.3.module">
            <sha256 value="d929b7f3a023021eb348b09114b5c6aa9aee9213cc3429962f064438fe5627a3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="besu-4.12.3.pom">
            <sha256 value="ba9e2dceabc448eb86843c27a59e8db1c2e03181185e457a40ab25d115e41d2f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="codegen" version="4.14.0">
         <artifact name="codegen-4.14.0.jar">
            <sha256 value="4996f6729e04fae89be2a3807fade85c878610765744e8ec17d2f0d455c7197c" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="codegen-4.14.0.module">
            <sha256 value="46c6f26476fef1c3e6aa7baa384125212cba364237d03455a85ba5a8ef4a6732" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="core" version="4.12.3">
         <artifact name="core-4.12.3.jar">
            <sha256 value="ba97edd9b86108c66657a0cff23e1afe9fd36ec287b60edde40b1915f7aedff0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="core-4.12.3.module">
            <sha256 value="7d910898785b14fb5fe8400d09d3ebbd7a1d07628321c69c19941159c224825e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="core-4.12.3.pom">
            <sha256 value="239b572cb1f34bf7cf25280c69a0b7b105565517eb1a5bd665a75384ac90efb3" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="core" version="4.14.0">
         <artifact name="core-4.14.0.jar">
            <sha256 value="525a7da603e5b915500d06d9ac42f878ba5adc51ca558f64d81d78df333aa8eb" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="core-4.14.0.module">
            <sha256 value="9774045d79044dffb6c4da7a42b456d8478c1b062e781ab4c2c8fce5121fcdd5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="core-4.14.0.pom">
            <sha256 value="70249b17ce334f0fbde83602d6f463ec0d4173d003523a7283107965966a4eae" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="crypto" version="4.12.3">
         <artifact name="crypto-4.12.3.jar">
            <sha256 value="91fd890d15f0fa1fafd51ea92127f3a1365387cf4bdc35da062280c405b2f91d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="crypto-4.12.3.module">
            <sha256 value="8beb2fceb11a28f71595419f40040fdb34e366d9326572b5bc559bcb638bb476" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="crypto-4.12.3.pom">
            <sha256 value="4e1dc4583a5213f1d2b164a4077a62696bb1e17a5f426f03bbf8437d1ba0b514" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="crypto" version="4.14.0">
         <artifact name="crypto-4.14.0.jar">
            <sha256 value="6d199e6234b81931535ff3833bb1e7046f2f7205836353e98e9a0f349c432dc1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="crypto-4.14.0.module">
            <sha256 value="c7170e0dc55df84ead3cb1a03c67f30740ef3974ce1f1aeca92fa7a397771b48" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="crypto-4.14.0.pom">
            <sha256 value="6a1b21ff8a4ce165b5b05929d899b092791c6d08234f746d8610ad60801a6891" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="eea" version="4.12.3">
         <artifact name="eea-4.12.3.jar">
            <sha256 value="ce27abfbc957ee7e6221e4f21d06542d9dc56f2285c176faec195a1a11dfcda5" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="eea-4.12.3.module">
            <sha256 value="68308a1fc0f408c922d1c6f5b9832030e3a2b94a192d75f8ad383fae2365dbf0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="eea-4.12.3.pom">
            <sha256 value="68135a070ae68e6dc226c2e323b823dac0b8ea4ddc2cd07f9aee90c86ed961f6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="org.web3j.gradle.plugin" version="4.14.0">
         <artifact name="org.web3j.gradle.plugin-4.14.0.pom">
            <sha256 value="d0c6c6f975fa88e3a575b3b32886b8320bd49dd229271b5988c573ca44dd29b7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="rlp" version="4.12.3">
         <artifact name="rlp-4.12.3.jar">
            <sha256 value="5f432a9d5ac8a9045f9ef0b73f9f113bcedd3c5ad4b92dcf22c4cd6f24f40b28" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="rlp-4.12.3.module">
            <sha256 value="7fdb7b7168bfd304b18714757796958df3cb53e013125af7a046fdb959b8913b" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="rlp-4.12.3.pom">
            <sha256 value="008f3ed978b0c77355a83092f8ed07ed79a9bc44f320105c861b86339de28217" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="rlp" version="4.14.0">
         <artifact name="rlp-4.14.0.jar">
            <sha256 value="f109a5400480c9014282df5944742ed54693b5579ae9575e0b50c4ccb8a9f4e0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="rlp-4.14.0.module">
            <sha256 value="12689f2cfa406c4d97b5f223bd75ab3ed5ab3789541178069bc0ce05a834a9c1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="rlp-4.14.0.pom">
            <sha256 value="b6b512137f8adcfe62b0920d1206cd9c925b2a27c72ddea079323cccd9f1c56d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="tuples" version="4.12.3">
         <artifact name="tuples-4.12.3.jar">
            <sha256 value="3304398c42ad1c362ed48af843a4029988eca7afb384ac7338e010ece94a0014" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuples-4.12.3.module">
            <sha256 value="3b143ef38e2a83a6c8df7d465a0432d94b92d6e572867ac918e86fd8827fcabc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuples-4.12.3.pom">
            <sha256 value="8b32c89941e264c023b3244ec0bc30f653d0eb40618465d01c71b7900deca215" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="tuples" version="4.14.0">
         <artifact name="tuples-4.14.0.jar">
            <sha256 value="f1dd23b0897f2a57ad35e4689b340fbd16fd4fdcd8229104a2630c0e175b5ae7" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuples-4.14.0.module">
            <sha256 value="d91550ac0a408039bc48976f94dfa9994dafe8e7a2cb2d4b1ba1689807364822" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="tuples-4.14.0.pom">
            <sha256 value="242295752f4efda6293f13f40928f3e6f59e971747a722ea8e37287a46cae4d4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="utils" version="4.12.3">
         <artifact name="utils-4.12.3.jar">
            <sha256 value="a19e7164da444d5cfd742b127452df4c85e026a7ac945f732778753d169f1ef4" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="utils-4.12.3.module">
            <sha256 value="21a3d55cfa7252342c34ea4ba2c4da733b8dc5afa2dd7432b5375ee4c86f8bf3" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="utils-4.12.3.pom">
            <sha256 value="17e673958a64bd32fa511758fafe056d120310d12660281a76ed8bad6a7126ef" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="utils" version="4.14.0">
         <artifact name="utils-4.14.0.jar">
            <sha256 value="fe94f4fdf282a703a8c4eec95c2e0d403e4d5dc5a97b24eea8c2336822c025c8" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="utils-4.14.0.module">
            <sha256 value="34732b30d619538f9e1212125479428a27241b1f4cb70c66655de089c27530b1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="utils-4.14.0.pom">
            <sha256 value="1b27f2439b36714ca35ec736f8f792a13067f3b26c2298e5b683ce183ab912eb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="web3j-gradle-plugin" version="4.14.0">
         <artifact name="web3j-gradle-plugin-4.14.0.jar">
            <sha256 value="b704975e916265aece1de455041a2ac0160a663665963c36a2995b338ef2af14" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="web3j-gradle-plugin-4.14.0.module">
            <sha256 value="005f1495bfd2139872f9460c1b63023abc6b58e4fe18f4937d0cd785dccf0295" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j" name="web3j-sokt" version="0.5.0">
         <artifact name="web3j-sokt-0.5.0.jar">
            <sha256 value="783a44cf8518785a60b873c337d4dd4a790833627c0f7ecccfa75fd7eac08587" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="web3j-sokt-0.5.0.module">
            <sha256 value="34f1c631a06da02a22e53b276df731b2d1d3f4407f57812739e83907aa390b49" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j.solidity" name="org.web3j.solidity.gradle.plugin" version="0.6.0">
         <artifact name="org.web3j.solidity.gradle.plugin-0.6.0.pom">
            <sha256 value="1368232378510a6465f3a86ff9f404eb3d79ecf593874eb99afb56ec1549d263" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.web3j.solidity" name="solidity-gradle-plugin" version="0.6.0">
         <artifact name="solidity-gradle-plugin-0.6.0.jar">
            <sha256 value="78dbd7a9eaf90a9b47a5adc457258f0797134fed9efdf97ca4514e7431c4539e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="solidity-gradle-plugin-0.6.0.module">
            <sha256 value="7d52f194512e677a7b1bd7317732be5f018564e8335594cf93f487fc8977cb43" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.xerial.snappy" name="snappy-java" version="1.1.10.7">
         <artifact name="snappy-java-1.1.10.7.jar">
            <sha256 value="4c766cb3f855415ee734b2392949a0b6f12a60879334a74518deaf6270d32e36" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="snappy-java-1.1.10.7.pom">
            <sha256 value="509da127808a02ed64238fa97a0ac5beb187c3d7a6c1bf910595cb51923f9cf4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="org.yaml" name="snakeyaml" version="2.3">
         <artifact name="snakeyaml-2.3.jar">
            <sha256 value="63a76fe66b652360bd4c2c107e6f0258daa7d4bb492008ba8c26fcd230ff9146" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="snakeyaml-2.3.pom">
            <sha256 value="0f5a265a06331b0049e352be32ca322de18b17f3d4dbb70635d40da692e3582f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="annotations" version="2.27.24">
         <artifact name="annotations-2.27.24.jar">
            <sha256 value="a681e9800c3508fd7d3130fe9dc2c1b5d66e0fbfc21579253fa961e6046d7bab" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="annotations-2.27.24.pom">
            <sha256 value="5277405b938ced7b8c1294a0c2458f8629138b9bf5dd2ca40242c2f0f5cb79ae" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="apache-client" version="2.27.24">
         <artifact name="apache-client-2.27.24.jar">
            <sha256 value="7ce42495ac7fd67b3548d40a1b6a65f1a3a4cca500341ade5023d130529420f0" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="apache-client-2.27.24.pom">
            <sha256 value="9d4d3fe22d0a0bbbbbacecfdeb34bcf9b6e3d849f3d7ed74793df07b566df1eb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="auth" version="2.27.24">
         <artifact name="auth-2.27.24.jar">
            <sha256 value="391fa523a8e69015c5ded495670da609172431314957ac1511acea2124a8dccc" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="auth-2.27.24.pom">
            <sha256 value="1a91fe202e2fe97e0b61a388d1afdbf333a5e398104bd6ad173733cbb703b719" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="aws-core" version="2.27.24">
         <artifact name="aws-core-2.27.24.jar">
            <sha256 value="2cd0e74f8c867115cdbb1c1191b61da0a9755eecad31c2228c687d047bd19fa6" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="aws-core-2.27.24.pom">
            <sha256 value="d5c057a961fbb28000236a1e30f3f6ae6d0dda0afdfb6a811cae3ee70a3197a0" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="aws-json-protocol" version="2.27.24">
         <artifact name="aws-json-protocol-2.27.24.jar">
            <sha256 value="3291cf6faa9d7cb98025775231862bb51034111d2e164dc2bcaad1c9c4513f4e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="aws-json-protocol-2.27.24.pom">
            <sha256 value="9b9c398606cd95fa6a3c50455fb76263424443743f397a61890b7a29df67873f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="aws-sdk-java-pom" version="2.27.24">
         <artifact name="aws-sdk-java-pom-2.27.24.pom">
            <sha256 value="f21519e2e91a47f1c79838c90543ac92d2596d426da4e52f9595016ffab18360" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="bom-internal" version="2.27.24">
         <artifact name="bom-internal-2.27.24.pom">
            <sha256 value="2fea493c6369ff67066ab5617f52bd116755b10fd0513c069897d752da433dfb" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="checksums" version="2.27.24">
         <artifact name="checksums-2.27.24.jar">
            <sha256 value="67a801c0aed923e92eae54c787b431b495cca1861fd6c4897aafad7f19ec8841" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="checksums-2.27.24.pom">
            <sha256 value="056ab12eba479441df67f1b263d8b62e97c36db158d68edaaa9c2bbf91c7461c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="checksums-spi" version="2.27.24">
         <artifact name="checksums-spi-2.27.24.jar">
            <sha256 value="ab7f4dfe87900f3d9fad04b6c997509cf4055e91c2410768a7d6058c68af585d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="checksums-spi-2.27.24.pom">
            <sha256 value="6e0f479d116b2d735589f028319dfd357c4becc5cb5f78edd3d66c210fe0701a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="core" version="2.27.24">
         <artifact name="core-2.27.24.pom">
            <sha256 value="27ae247e3f36b269339f3bc6245b421fb959b21400f9ea291a53649ce2f3b4ef" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="endpoints-spi" version="2.27.24">
         <artifact name="endpoints-spi-2.27.24.jar">
            <sha256 value="ffca7f1576641eaf7918a2352f97fa6e3f31729131138a2b1caceab2d05eb9ab" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="endpoints-spi-2.27.24.pom">
            <sha256 value="45a6323752b751dae05bf503aed49e218f3f7ff344adf3a69fac49e8e2ed1b53" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="http-auth" version="2.27.24">
         <artifact name="http-auth-2.27.24.jar">
            <sha256 value="7da2b663affe20edab111645a2105c3e47830ede540207eace81b9855d0108be" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="http-auth-2.27.24.pom">
            <sha256 value="9417188a10da2c34c92e6793fb22b29c87be3a1b81b3d791346ab455f5faf5d1" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="http-auth-aws" version="2.27.24">
         <artifact name="http-auth-aws-2.27.24.jar">
            <sha256 value="0e61034fa9db582e55a6dcf5d8ab4c48f37e2684e47327ab4bff5799ac17a20d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="http-auth-aws-2.27.24.pom">
            <sha256 value="91c1a8588fac75baff7b707eaf731c5617e2e5f783496723a07712b613cac892" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="http-auth-aws-eventstream" version="2.27.24">
         <artifact name="http-auth-aws-eventstream-2.27.24.jar">
            <sha256 value="277b9717852fda69c3bda0b58de0d8f0522ab07235a02c24772a854638c91f04" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="http-auth-aws-eventstream-2.27.24.pom">
            <sha256 value="35d8f93665bd47d274bfb36f55df53499d8ed261eddf137a8153fc4cc7e93711" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="http-auth-spi" version="2.27.24">
         <artifact name="http-auth-spi-2.27.24.jar">
            <sha256 value="fff2dbe3a8fdba6bf357f14f59e02d9781e0a1d780fc5f901dd3d8de664437c2" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="http-auth-spi-2.27.24.pom">
            <sha256 value="aa54d77612f416796af009b5b9fe3fad5f97b6a9e2d8677a6507b07fa6cc0e81" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="http-client-spi" version="2.27.24">
         <artifact name="http-client-spi-2.27.24.jar">
            <sha256 value="409c24f1f71752fbade652455d495571cbcfeefb2c6e4ea71e3bda945248cb3e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="http-client-spi-2.27.24.pom">
            <sha256 value="9fe8a7650d5440f8c9c44eb7e2db70d5a8f8ac29291edc6c48d6f28fc5c54fd4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="http-clients" version="2.27.24">
         <artifact name="http-clients-2.27.24.pom">
            <sha256 value="488b5f9ea33fffd8ed964c0e365401c6e676008153cca255fd7794bd51dfc5d8" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="identity-spi" version="2.27.24">
         <artifact name="identity-spi-2.27.24.jar">
            <sha256 value="cd541db1f6082eb6e7934d1c3d361149cacbfe6fc25bdb8d4b2d5abd3ad341c1" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="identity-spi-2.27.24.pom">
            <sha256 value="6bd9893cca52621e6d1fd845b7889d7bc1f900efcb95f870cd3870daa987d807" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="json-utils" version="2.27.24">
         <artifact name="json-utils-2.27.24.jar">
            <sha256 value="be8cda8af97e1ae0385cbbb75e405acd349136b5371ef798c66fdbf406b12b13" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="json-utils-2.27.24.pom">
            <sha256 value="11a1d6d3e52f2b75b9cf8fa28095706a29b0df90835479d63010e2bdf41048d4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="kms" version="2.27.24">
         <artifact name="kms-2.27.24.jar">
            <sha256 value="ed625f9f70f639af02690b4309b724cc707fb68c1a627ec05b45c99b5db6c2ee" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="kms-2.27.24.pom">
            <sha256 value="cb3ab05c5ce062743f87e151275030bc1fa9fc1c4c1461c156825a8781d64e3c" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="metrics-spi" version="2.27.24">
         <artifact name="metrics-spi-2.27.24.jar">
            <sha256 value="b9de1e33797bfebfc44cfada922f8e7c86bb24072942871cb9698f341b933f60" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="metrics-spi-2.27.24.pom">
            <sha256 value="9ae84807b13cb1c4b9ba1d03161cfcd1bcd7397a30c48d50f581845541eb710f" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="netty-nio-client" version="2.27.24">
         <artifact name="netty-nio-client-2.27.24.jar">
            <sha256 value="22e9c21d6bce99208e55227f046a7eed2ebb34ac599bddf5467eb9acab7bf49d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="netty-nio-client-2.27.24.pom">
            <sha256 value="51a6a033b307c0389d53b9cb4960761f8b67cefaef9a94428321b0805ffaa248" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="profiles" version="2.27.24">
         <artifact name="profiles-2.27.24.jar">
            <sha256 value="041ffa5057b19a51b1a83de5c8185e773c4bce7f0c7006bdb5f0f9f1c0a80772" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="profiles-2.27.24.pom">
            <sha256 value="f1c98023ec018d7e7818500c0b9fc00d4bbe3e23219d33fe5aaf563ba8bec4ae" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="protocol-core" version="2.27.24">
         <artifact name="protocol-core-2.27.24.jar">
            <sha256 value="55784ed7a44c9eaa7d786c2555b085ae2f898007b8712f0ee7755726d795296f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="protocol-core-2.27.24.pom">
            <sha256 value="7f0c058b8a8e38163ae0ad0178be09fc2b6a8326297993b9b8b621c1e37cfc21" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="protocols" version="2.27.24">
         <artifact name="protocols-2.27.24.pom">
            <sha256 value="2845e6ac6bc85d9cbc6f6d97aa3e55c863dd2cb5a4f463dc849a75c33a8bdb8a" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="regions" version="2.27.24">
         <artifact name="regions-2.27.24.jar">
            <sha256 value="89a32da53206a1e1f4a898bc3926d66e33db3b3a9df3086d26d35a4e4150a187" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="regions-2.27.24.pom">
            <sha256 value="55653673064716b050eda2ff5f1c58c7fb1b7be4f1369a0eddc68f02261dc6c6" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="retries" version="2.27.24">
         <artifact name="retries-2.27.24.jar">
            <sha256 value="a068fa4e9e3eca8c9ab856b30d3d16e2815a7e24c6c0a67240ae77afeca7e29e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="retries-2.27.24.pom">
            <sha256 value="767850dc58af0798b18e15855b8823cf43cf43d41fd40ee5a619bf62e9643423" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="retries-spi" version="2.27.24">
         <artifact name="retries-spi-2.27.24.jar">
            <sha256 value="a66cec1c63d998886b407a4835143708d64004f9c7a4d651de54547e2b899d31" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="retries-spi-2.27.24.pom">
            <sha256 value="9e0388f7e65cd3fa2eb1d674b0a726cd1814430df524c898047a38e41168a288" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="sdk-core" version="2.27.24">
         <artifact name="sdk-core-2.27.24.jar">
            <sha256 value="dbd78fb0c0489bdbe0f2887b54a7f26db21fc7055197fd20619e1a4d4fab6a8f" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="sdk-core-2.27.24.pom">
            <sha256 value="dcfc907208ebe10aadd32a1272a753f88a7793486bc22c0b0b744df42e5f82f7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="services" version="2.27.24">
         <artifact name="services-2.27.24.pom">
            <sha256 value="de2a9c27bc51e2e45bab72e41147463099af4934340591f16c41cc9f69dcbfd4" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="third-party" version="2.27.24">
         <artifact name="third-party-2.27.24.pom">
            <sha256 value="81b1d0029aa3828392cbe8dfc46d56ebf111219de8284d5e2295e0f9cc79adfc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="third-party-jackson-core" version="2.27.24">
         <artifact name="third-party-jackson-core-2.27.24.jar">
            <sha256 value="b0e47d2c8dc4e9a5a821ce7c4cfb8850578de1c2c66767e479a98270c2be988e" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="third-party-jackson-core-2.27.24.pom">
            <sha256 value="6e367ec8d6e808e8fe9e42a388bf4ca97b266f1480e237280c1d37f06ae4483d" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.awssdk" name="utils" version="2.27.24">
         <artifact name="utils-2.27.24.jar">
            <sha256 value="2f5eec07b5f691cb83d9c975881801e6acd123bb1f790d8503af13366f0bb06a" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="utils-2.27.24.pom">
            <sha256 value="4916edb4cd721cbecc1650ceffb69d8bdfda3db1924164ad64591fe8001d6236" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="software.amazon.eventstream" name="eventstream" version="1.0.1">
         <artifact name="eventstream-1.0.1.jar">
            <sha256 value="0c37d8e696117f02c302191b8110b0d0eb20fa412fce34c3a269ec73c16ce822" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="eventstream-1.0.1.pom">
            <sha256 value="f9460cb794a0a7af68277efb5769569e8e66515250276c37e62a7e8bd4b257cc" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="tech.pegasys.discovery" name="discovery" version="25.4.0">
         <artifact name="discovery-25.4.0.jar">
            <sha256 value="b0f75d841ecc767b1feba57a41e642ede3ea27ac4855e5252b69f1e38528947d" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="discovery-25.4.0.module">
            <sha256 value="512415ad79f50a42dfd27009defdf885add7e73c4925b8c402937b6c6515a430" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="discovery-25.4.0.pom">
            <sha256 value="20770e2879c78abc10971f77e32b20f091b2090e0ca7edb8e09d01787468d3a7" origin="Generated by Gradle"/>
         </artifact>
      </component>
      <component group="tools.profiler" name="async-profiler" version="4.0">
         <artifact name="async-profiler-4.0.jar">
            <sha256 value="177f340015b0e52b6673afb38a07edb12d58cf27920a4dc9ba9e76e9d0eca986" origin="Generated by Gradle"/>
         </artifact>
         <artifact name="async-profiler-4.0.pom">
            <sha256 value="a9d54a91e35341ae9f5c749ed67db07bab72ff4c07799233d1049808c61cd44e" origin="Generated by Gradle"/>
         </artifact>
      </component>
   </components>
</verification-metadata>
