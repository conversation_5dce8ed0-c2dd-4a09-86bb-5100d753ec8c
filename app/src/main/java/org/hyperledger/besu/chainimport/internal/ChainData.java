/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package org.hyperledger.besu.chainimport.internal;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/** The Chain data. */
@JsonIgnoreProperties("comment")
public class ChainData {

  private final List<BlockData> blocks;

  /**
   * Instantiates a new Chain data.
   *
   * @param blocks the blocks
   */
  @JsonCreator
  public ChainData(@JsonProperty("blocks") final List<BlockData> blocks) {
    this.blocks = blocks;
  }

  /**
   * Gets blocks.
   *
   * @return the blocks
   */
  public List<BlockData> getBlocks() {
    return blocks;
  }
}
