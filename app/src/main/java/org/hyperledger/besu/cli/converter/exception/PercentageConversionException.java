/*
 * Copyright ConsenSys AG.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package org.hyperledger.besu.cli.converter.exception;

import static java.lang.String.format;

/** The custom Percentage conversion exception. */
public final class PercentageConversionException extends Exception {

  /**
   * Instantiates a new Percentage conversion exception.
   *
   * @param value the invalid value to add in exception message
   */
  public PercentageConversionException(final String value) {
    super(format("Invalid value: %s, should be a number between 0 and 100 inclusive.", value));
  }
}
