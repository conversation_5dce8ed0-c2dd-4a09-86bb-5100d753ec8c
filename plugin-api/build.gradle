/*
 * Copyright Hyperledger Besu Contributors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 */

import java.security.MessageDigest

apply plugin: 'java-library'
jar {
  archiveBaseName = calculateArtifactId(project)
  manifest {
    attributes(
      'Specification-Title': archiveBaseName,
      'Specification-Version': project.version,
      'Implementation-Title': archiveBaseName,
      'Implementation-Version': calculateVersion(),
      'Commit-Hash': getGitCommitDetails(40).hash,
      'Automatic-Module-Name': 'org.hyperledger.besu.plugin.api'
      )
  }
}

dependencies {
  api project(':datatypes')
  api 'org.apache.commons:commons-lang3'
  api 'io.consensys.tuweni:tuweni-bytes'
  api 'io.consensys.tuweni:tuweni-units'
  implementation 'com.google.guava:guava'
  implementation project(':evm')
  compileOnly 'io.vertx:vertx-core'
}

configurations { testArtifacts }

class FileStateChecker extends DefaultTask {

  @Input
  Set<File> files

  @Input
  String knownHash

  @TaskAction
  def CheckState() {
    def digestor = MessageDigest.getInstance("SHA-256")

    this.files.toSorted(Comparator.comparing({it.canonicalPath})).each {
      digestor.update(it.readBytes())
    }
    def currentHash = digestor.digest().encodeBase64().toString()
    if (this.knownHash != currentHash) {
      throw new GradleException("""For the Plugin APIs the checksum of the project did not match what was expected.

If this is a deliberate change where you have thought through backwards compatibility,
then update "Expected" for "Calculated" in the appropriate build.gradle as the knownHash for this task.
Expected   : ${this.knownHash}
Calculated : ${currentHash}
""")
    }
  }
}

tasks.register('checkAPIChanges', FileStateChecker) {
  description = "Checks that the API for the Plugin-API project does not change without deliberate thought"
  files = sourceSets.main.allJava.files
  knownHash = '2iFZ9OBdtl1VoQLGPdqolq8zlBIkwszjin8vO4TkTqk='
}
check.dependsOn('checkAPIChanges')

publishing {
  publications {
    mavenJava(MavenPublication) {
      groupId 'org.hyperledger.besu'
      pom {
        name = 'Besu Plugins Library'
        description = 'Core Plugins Libraries for Besu'
      }
    }
  }
}
