/*
 * Copyright contributors to Hyperledger Besu.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package org.hyperledger.besu.cli.converter.exception;

import static java.lang.String.format;

/** The custom Duration conversion exception. */
public final class DurationConversionException extends Exception {

  /**
   * Instantiates a new Duration conversion exception for malformed value.
   *
   * @param value the value
   */
  public DurationConversionException(final String value) {
    super(format("'%s' is not a long", value));
  }

  /**
   * Instantiates a new Duration conversion exception for negative value.
   *
   * @param value the millis
   */
  public DurationConversionException(final long value) {
    super(format("negative value '%d' is not allowed", value));
  }
}
