/*
 * Copyright contributors to Hyperledger Besu.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * SPDX-License-Identifier: Apache-2.0
 */
package org.hyperledger.besu.cli.config;

import org.hyperledger.besu.cli.util.ProfileFinder;

import java.util.Iterator;
import java.util.Set;
import java.util.TreeSet;

/** Provides a list of profile names that can be used for command line completion. */
public class ProfilesCompletionCandidates implements Iterable<String> {
  /**
   * Create a new instance of ProfilesCompletionCandidates. This constructor is required for
   * Picocli.
   */
  public ProfilesCompletionCandidates() {}

  @Override
  public Iterator<String> iterator() {
    final Set<String> profileNames = new TreeSet<>(InternalProfileName.getInternalProfileNames());
    profileNames.addAll(ProfileFinder.getExternalProfileNames());
    return profileNames.iterator();
  }
}
